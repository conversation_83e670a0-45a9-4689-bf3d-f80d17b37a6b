# IAM Role
data "aws_iam_policy_document" "assume_role" {
  statement {
    sid     = "RDSAssume"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
  }
}

data "aws_iam_policy_document" "decrypt_secrets" {
  statement {
    sid     = "DecryptSecrets"
    effect  = "Allow"
    actions = ["kms:Decrypt"]
    resources = coalescelist(
      var.kms_key_arn
    )
  }

  statement {
    sid    = "ListSecrets"
    effect = "Allow"
    actions = [
      "secretsmanager:GetRandomPassword",
      "secretsmanager:ListSecrets",
    ]
    resources = ["*"]
  }

  statement {
    sid    = "GetSecrets"
    effect = "Allow"
    actions = [
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
      "secretsmanager:ListSecretVersionIds",
    ]

    resources = distinct([for auth in var.auth : auth.secret_arn])
  }
}