# ECS Cluster
resource "aws_ecs_cluster" "this" {
  name = join("-", [var.program, var.project, var.environment, var.name])

  setting {
    name  = "containerInsights"
    value = var.container_insights ? "enabled" : "disabled"
  }

  tags = var.tags
}

# Create service-linked role for ECS if specified by variable
resource "aws_iam_service_linked_role" "ecs" {
  count            = var.create_ecs_service_linked_role ? 1 : 0
  aws_service_name = "ecs.amazonaws.com"
  description      = "Service-linked role for Amazon ECS"
}

# IAM Role for ECS Task Execution
resource "aws_iam_role" "this" {
  count = var.create_task_execution_role ? 1 : 0
  name  = join("-", [var.program, var.project, var.environment, var.name, "task-execution-role"])

  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role.json

  tags = var.tags
}

# Attach AWS managed policy for ECS task execution
resource "aws_iam_role_policy_attachment" "this" {
  count      = var.create_task_execution_role ? 1 : 0
  role       = aws_iam_role.this[0].name
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}
