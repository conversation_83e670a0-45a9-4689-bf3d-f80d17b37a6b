# terraform-aws-amazonmq

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |
| <a name="requirement_random"></a> [random](#requirement\_random) | ~> 3.5 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_mq_broker.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/mq_broker) | resource |
| [aws_mq_configuration.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/mq_configuration) | resource |
| [aws_secretsmanager_secret.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret) | resource |
| [aws_secretsmanager_secret_version.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_version) | resource |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_vpc_security_group_egress_rule.broker_allow_all](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.broker_management_console](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.broker_subnet_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_secretsmanager_random_password.broker_password](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/secretsmanager_random_password) | data source |
| [aws_secretsmanager_secret_version.existing](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/secretsmanager_secret_version) | data source |
| [aws_subnet.subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnet) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_apply_immediately"></a> [apply\_immediately](#input\_apply\_immediately) | Whether to apply changes immediately or during maintenance window | `bool` | `false` | no |
| <a name="input_auto_minor_version_upgrade"></a> [auto\_minor\_version\_upgrade](#input\_auto\_minor\_version\_upgrade) | Whether to automatically upgrade to new minor versions | `bool` | `true` | no |
| <a name="input_broker_configuration"></a> [broker\_configuration](#input\_broker\_configuration) | AmazonMQ configuration data for Magento. If not provided, default configuration optimized for Magento will be used. | `string` | `"# Default RabbitMQ configuration with optimizations for Magento\n# Increase file descriptors limit\ntotal_memory_available_override_value = 0.8\n\n# Disk free limit - stop accepting messages when disk space drops below this\ndisk_free_limit.absolute = 2GB\n\n# Memory threshold - flow control is triggered when memory usage exceeds this\nvm_memory_high_watermark.relative = 0.7\n\n# Increase default timeouts for better handling of large messages\nconsumer_timeout = 3600000\n\n# Increase message size limit for large payloads\nmax_message_size = 134217728\n"` | no |
| <a name="input_broker_instance_type"></a> [broker\_instance\_type](#input\_broker\_instance\_type) | Instance type of the broker | `string` | `"mq.t3.micro"` | no |
| <a name="input_create_secrets_manager_secret"></a> [create\_secrets\_manager\_secret](#input\_create\_secrets\_manager\_secret) | Whether to create a new AWS Secrets Manager secret for AmazonMQ credentials. This is always true unless secrets\_manager\_secret\_id is provided. | `bool` | `true` | no |
| <a name="input_deployment_mode"></a> [deployment\_mode](#input\_deployment\_mode) | Deployment mode of the broker | `string` | `"SINGLE_INSTANCE"` | no |
| <a name="input_engine_type"></a> [engine\_type](#input\_engine\_type) | Type of broker engine | `string` | `"RabbitMQ"` | no |
| <a name="input_engine_version"></a> [engine\_version](#input\_engine\_version) | Version of the broker engine | `string` | `"3.13"` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment stage | `string` | n/a | yes |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS key ID for encryption at rest | `string` | `null` | no |
| <a name="input_maintenance_window_start_time"></a> [maintenance\_window\_start\_time](#input\_maintenance\_window\_start\_time) | Configuration for the maintenance window start time | <pre>object({<br/>    day_of_week = string<br/>    time_of_day = string<br/>    time_zone   = string<br/>  })</pre> | <pre>{<br/>  "day_of_week": "SUNDAY",<br/>  "time_of_day": "02:00",<br/>  "time_zone": "UTC"<br/>}</pre> | no |
| <a name="input_name"></a> [name](#input\_name) | Name for the AmazonMQ resources | `string` | `"magento"` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | n/a | yes |
| <a name="input_publicly_accessible"></a> [publicly\_accessible](#input\_publicly\_accessible) | Whether the broker should be publicly accessible | `bool` | `false` | no |
| <a name="input_secrets_manager_kms_key_id"></a> [secrets\_manager\_kms\_key\_id](#input\_secrets\_manager\_kms\_key\_id) | KMS Key ID for encrypting secrets stored in AWS Secrets Manager | `string` | `null` | no |
| <a name="input_secrets_manager_secret_id"></a> [secrets\_manager\_secret\_id](#input\_secrets\_manager\_secret\_id) | The ID of an existing AWS Secrets Manager secret containing AmazonMQ credentials. The secret must contain 'username' and 'password' keys. | `string` | `null` | no |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | List of subnet IDs where broker will be deployed | `list(string)` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |
| <a name="input_username"></a> [username](#input\_username) | Username for broker access. Not required if using secrets\_manager\_secret\_id. | `string` | `"magento"` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | VPC ID where AmazonMQ will be deployed | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_this_broker_arn"></a> [this\_broker\_arn](#output\_this\_broker\_arn) | The ARN of the AmazonMQ broker |
| <a name="output_this_broker_id"></a> [this\_broker\_id](#output\_this\_broker\_id) | The ID of the AmazonMQ broker |
| <a name="output_this_broker_name"></a> [this\_broker\_name](#output\_this\_broker\_name) | The name of the AmazonMQ broker |
| <a name="output_this_configuration_id"></a> [this\_configuration\_id](#output\_this\_configuration\_id) | The ID of the AmazonMQ configuration |
| <a name="output_this_configuration_revision"></a> [this\_configuration\_revision](#output\_this\_configuration\_revision) | The revision of the AmazonMQ configuration |
| <a name="output_this_console_url"></a> [this\_console\_url](#output\_this\_console\_url) | The URL of the primary AmazonMQ web console |
| <a name="output_this_endpoint"></a> [this\_endpoint](#output\_this\_endpoint) | The endpoint of the primary AmazonMQ broker |
| <a name="output_this_ip_address"></a> [this\_ip\_address](#output\_this\_ip\_address) | The IP address of the primary AmazonMQ broker |
| <a name="output_this_secrets_manager_secret_arn"></a> [this\_secrets\_manager\_secret\_arn](#output\_this\_secrets\_manager\_secret\_arn) | The ARN of the AWS Secrets Manager secret containing AmazonMQ credentials (if created) |
| <a name="output_this_secrets_manager_secret_id"></a> [this\_secrets\_manager\_secret\_id](#output\_this\_secrets\_manager\_secret\_id) | The ID of the AWS Secrets Manager secret containing AmazonMQ credentials (if created) |
| <a name="output_this_security_group_id"></a> [this\_security\_group\_id](#output\_this\_security\_group\_id) | The ID of the security group for the AmazonMQ broker |
<!-- END_TF_DOCS -->
