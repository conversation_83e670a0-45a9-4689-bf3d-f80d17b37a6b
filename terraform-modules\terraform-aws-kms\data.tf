data "aws_caller_identity" "current" {}

data "aws_iam_policy_document" "default_kms_policy" {
  statement {
    sid    = "Enable IAM User Permissions"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions   = ["kms:*"]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "custom_kms_policy" {
  count = var.policy.Version != null && var.policy.Statement != null ? 1 : 0

  version = var.policy.Version

  dynamic "statement" {
    for_each = var.policy.Statement != null ? var.policy.Statement : []

    content {
      sid       = statement.value.Sid
      effect    = statement.value.Effect
      actions   = flatten([statement.value.Action])
      resources = flatten([statement.value.Resource])

      dynamic "principals" {
        for_each = statement.value.Principal != null ? {
          for type, ids in statement.value.Principal : type => ids
        } : {}

        content {
          type        = principals.key
          identifiers = flatten([principals.value])
        }
      }

      dynamic "condition" {
        for_each = statement.value.Condition != null ? flatten([
          for test, vars in statement.value.Condition : [
            for var, vals in vars : {
              test     = test
              variable = var
              values   = vals
            }
          ]
        ]) : []

        content {
          test     = condition.value.test
          variable = condition.value.variable
          values   = flatten([condition.value.values])
        }
      }
    }
  }
}
