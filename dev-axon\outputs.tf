output "adapter_api_task_definition_arn" {
  description = "ARN of the Adapter API task definition"
  value       = module.adapter_api.task_definition_arn
}

output "adapter_api_service_id" {
  description = "ID of the Adapter API ECS service"
  value       = module.adapter_api.service_id
}

output "adapter_api_service_name" {
  description = "Name of the Adapter API ECS service"
  value       = module.adapter_api.service_name
}

output "adapter_api_task_role_arn" {
  description = "ARN of the IAM role used by the Adapter API tasks"
  value       = aws_iam_role.adapter_api_task_role.arn
}

output "adapter_api_execution_role_arn" {
  description = "ARN of the IAM role used for ECS task execution"
  value       = aws_iam_role.adapter_api_task_execution_role.arn
}

output "adapter_api_security_group_id" {
  description = "ID of the security group for the Adapter API service"
  value       = aws_security_group.adapter_api.id
}

output "adapter_api_cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for Adapter API"
  value       = module.adapter_api.cloudwatch_log_group_name
}

output "adapter_api_ecr_repository_url" {
  description = "URL of the ECR repository for Adapter API"
  value       = module.adapter_api.ecr_repository_url
}

output "adapter_api_ecr_repository_arn" {
  description = "ARN of the ECR repository for Adapter API"
  value       = module.adapter_api.ecr_repository_arn
}

output "adapter_api_image_note" {
  description = "Note about how to use the adapter API image"
  value       = "To use the ECR repository, set adapter_api_image to: ${module.adapter_api.ecr_repository_url}:<tag>"
}