variable "program" {
  description = "The name of the overarching program or initiative"
  type        = string
}

variable "project" {
  description = "The name of the specific project within the program"
  type        = string
}

variable "environment" {
  description = "The deployment environment (e.g., dev, staging, prod)"
  type        = string
}

variable "name" {
  description = "Name of the ECS task/service"
  type        = string
}

variable "cluster_id" {
  description = "ID of the ECS cluster to deploy to"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the ECS service"
  type        = list(string)
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

variable "cpu" {
  description = "CPU units for the task"
  type        = number
  default     = 256
}

variable "memory" {
  description = "Memory (in MB) for the task"
  type        = number
  default     = 512
}

variable "container_definitions" {
  description = "Container definitions for the task"
  type        = string
}

variable "desired_count" {
  description = "Desired number of tasks"
  type        = number
  default     = 1
}

variable "launch_type" {
  description = "Launch type for the service (FARGATE or EC2)"
  type        = string
  default     = "FARGATE"
}

variable "platform_version" {
  description = "Platform version for Fargate"
  type        = string
  default     = "LATEST"
}

variable "assign_public_ip" {
  description = "Whether to assign a public IP to the task"
  type        = bool
  default     = false
}

variable "security_group_ids" {
  description = "List of security group IDs for the service"
  type        = list(string)
}

variable "enable_service_discovery" {
  description = "Whether to enable service discovery"
  type        = bool
  default     = false
}

variable "service_discovery_namespace_id" {
  description = "ID of the service discovery namespace"
  type        = string
  default     = ""
}

variable "load_balancer_config" {
  description = "Load balancer configuration for the service"
  type = list(object({
    target_group_arn = string
    container_name   = string
    container_port   = number
  }))
  default = []
}

variable "runtime_platform" {
  description = "Runtime platform configuration"
  type = object({
    cpu_architecture        = string
    operating_system_family = string
  })
  default = {
    cpu_architecture        = "X86_64"
    operating_system_family = "LINUX"
  }
}

variable "enable_ecs_managed_tags" {
  description = "Whether to enable ECS managed tags"
  type        = bool
  default     = true
}

variable "propagate_tags" {
  description = "Whether to propagate tags from the service to tasks"
  type        = string
  default     = "SERVICE"
}

variable "deployment_maximum_percent" {
  description = "Maximum percent for deployment"
  type        = number
  default     = 200
}

variable "deployment_minimum_healthy_percent" {
  description = "Minimum healthy percent for deployment"
  type        = number
  default     = 100
}

variable "health_check_grace_period_seconds" {
  description = "Health check grace period in seconds"
  type        = number
  default     = null
}

variable "create_cloudwatch_log_group" {
  description = "Whether to create a CloudWatch log group"
  type        = bool
  default     = true
}

variable "cloudwatch_log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 7
}

variable "create_ecr_repository" {
  description = "Whether to create an ECR repository for this service"
  type        = bool
  default     = false
}

variable "ecr_image_tag_mutability" {
  description = "The tag mutability setting for the ECR repository"
  type        = string
  default     = "MUTABLE"
}

variable "ecr_scan_on_push" {
  description = "Whether to scan images on push to ECR"
  type        = bool
  default     = true
}

variable "ecr_lifecycle_policy_count" {
  description = "Number of images to keep in ECR repository"
  type        = number
  default     = 10
}