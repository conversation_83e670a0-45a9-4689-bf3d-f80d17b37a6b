module "network" {
  source = "../skeletons/terraform-skeleton-network"

  program     = var.program
  project     = var.project
  environment = var.environment

  cidr_block        = var.cidr_block
  public_subnets    = var.public_subnets
  private_subnets   = var.private_subnets
  protected_subnets = var.protected_subnets

  # Simplified VPN configuration
  customer_gateway_ip        = var.customer_gateway_ip
  bgp_asn                    = var.bgp_asn
  vpn_static_routes_only     = var.vpn_static_routes_only
  vpn_destination_cidr_blocks = var.vpn_destination_cidr_blocks
  vpn_alarm_actions          = var.vpn_alarm_actions
  vpn_ok_actions             = var.vpn_ok_actions
}
