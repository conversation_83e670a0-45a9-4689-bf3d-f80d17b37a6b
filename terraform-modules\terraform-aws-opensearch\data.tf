data "aws_subnet" "subnets" {
  for_each = toset(var.subnet_ids)
  id       = each.value
}

data "aws_iam_policy_document" "cloudwatch_log_publishing_policy" {
  dynamic "statement" {
    for_each = var.enable_logs ? [1] : []
    content {
      effect = "Allow"
      principals {
        type        = "Service"
        identifiers = ["es.amazonaws.com"]
      }
      actions = [
        "logs:PutLogEvents",
        "logs:CreateLogStream"
      ]
      resources = [for log_group in aws_cloudwatch_log_group.opensearch_logs : log_group.arn]
    }
  }
}

data "aws_iam_policy_document" "opensearch_access_policy" {
  statement {
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    actions   = ["es:*"]
    resources = ["arn:aws:es:*:*:domain/${var.project}-${var.environment}-${var.name}/*"]

    dynamic "condition" {
      for_each = var.access_policy_conditions
      content {
        test     = condition.value.test
        variable = condition.value.variable
        values   = condition.value.values
      }
    }
  }
}
