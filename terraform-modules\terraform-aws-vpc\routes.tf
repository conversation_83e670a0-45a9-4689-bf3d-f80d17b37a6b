resource "aws_route_table" "public" {
  vpc_id = aws_vpc.this.id

  tags = {
    Name = join("-", [var.program, var.environment, "rt", "public"])
  }
}

resource "aws_route" "igw" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.this.id
}

resource "aws_route_table_association" "public" {
  for_each = aws_subnet.public

  route_table_id = aws_route_table.public.id
  subnet_id      = each.value.id

}

resource "aws_route_table" "private" {
  for_each = aws_subnet.private

  vpc_id = aws_vpc.this.id

  tags = {
    Name = join("-", [var.program, var.environment, "rt", "private", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }
}

resource "aws_route" "natgw" {
  for_each = aws_nat_gateway.this

  route_table_id         = aws_route_table.private[each.key].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = each.value.id
}

resource "aws_route_table_association" "private" {
  for_each = aws_subnet.private

  route_table_id = aws_route_table.private[each.key].id
  subnet_id      = each.value.id
}

resource "aws_route_table" "protected" {
  vpc_id = aws_vpc.this.id

  tags = {
    Name = join("-", [var.program, var.environment, "rt", "protected"])
  }
}

resource "aws_route_table_association" "protected" {
  for_each = aws_subnet.protected

  route_table_id = aws_route_table.protected.id
  subnet_id      = each.value.id
}
