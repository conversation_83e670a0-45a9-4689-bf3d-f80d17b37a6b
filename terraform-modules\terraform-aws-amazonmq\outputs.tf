output "this_broker_id" {
  description = "The ID of the AmazonMQ broker"
  value       = aws_mq_broker.this.id
}

output "this_broker_arn" {
  description = "The ARN of the AmazonMQ broker"
  value       = aws_mq_broker.this.arn
}

output "this_broker_name" {
  description = "The name of the AmazonMQ broker"
  value       = aws_mq_broker.this.broker_name
}

output "this_console_url" {
  description = "The URL of the primary AmazonMQ web console"
  value       = aws_mq_broker.this.instances[0].console_url
}

output "this_ip_address" {
  description = "The IP address of the primary AmazonMQ broker"
  value       = aws_mq_broker.this.instances[0].ip_address
}

output "this_endpoint" {
  description = "The endpoint of the primary AmazonMQ broker"
  value       = aws_mq_broker.this.instances[0].endpoints[0]
}

output "this_configuration_id" {
  description = "The ID of the AmazonMQ configuration"
  value       = aws_mq_configuration.this.id
}

output "this_configuration_revision" {
  description = "The revision of the AmazonMQ configuration"
  value       = aws_mq_configuration.this.latest_revision
}

output "this_security_group_id" {
  description = "The ID of the security group for the AmazonMQ broker"
  value       = aws_security_group.this.id
}

output "this_secrets_manager_secret_id" {
  description = "The ID of the AWS Secrets Manager secret containing AmazonMQ credentials (if created)"
  value       = var.create_secrets_manager_secret ? aws_secretsmanager_secret.this[0].id : data.aws_secretsmanager_secret_version.existing[0].secret_id
}

output "this_secrets_manager_secret_arn" {
  description = "The ARN of the AWS Secrets Manager secret containing AmazonMQ credentials (if created)"
  value       = var.create_secrets_manager_secret ? aws_secretsmanager_secret.this[0].arn : data.aws_secretsmanager_secret_version.existing[0].arn
}
