#trivy:ignore:aws-autoscaling-enable-at-rest-encryption
resource "aws_vpc" "this" {
  cidr_block           = var.cidr_block
  enable_dns_support   = var.enable_dns_support
  enable_dns_hostnames = var.enable_dns_hostnames

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "vpc"])
  }
}

resource "aws_internet_gateway" "this" {
  vpc_id = aws_vpc.this.id

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "igw"])
  }
}
