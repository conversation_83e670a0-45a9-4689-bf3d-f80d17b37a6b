variable "program" {
  description = "Program name"
  type        = string
  default     = "evolution"
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "foundation"
}

variable "environment" {
  description = "Environment stage"
  type        = string
  default     = "dev"
}

variable "region" {
  description = "AWS Region"
  default     = "us-east-2"
}

variable "cidr_block" {
  description = "vpc cidr block (X.X.X.X/X notation)"
  type        = string
  default     = "**********/16"
}

variable "public_subnets" {
  description = "List of public subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
  default = [
    {
      cidr_block        = "**********/24"
      availability_zone = "us-east-2a"
    },
    {
      cidr_block        = "***********/24"
      availability_zone = "us-east-2b"
    },
    {
      cidr_block        = "************/24"
      availability_zone = "us-east-2c"
    }
  ]
}

variable "private_subnets" {
  description = "List of private subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
  default = [
    {
      cidr_block        = "***********/20"
      availability_zone = "us-east-2a"
    },
    {
      cidr_block        = "***********/20"
      availability_zone = "us-east-2b"
    },
    {
      cidr_block        = "************/20"
      availability_zone = "us-east-2c"
    }
  ]
}

variable "protected_subnets" {
  description = "List of protected subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
  default = [
    {
      cidr_block        = "***********/22"
      availability_zone = "us-east-2a"
    },
    {
      cidr_block        = "************/22"
      availability_zone = "us-east-2b"
    },
    {
      cidr_block        = "************/22"
      availability_zone = "us-east-2c"
    }
  ]
}

variable "customer_gateway_ip" {
  description = "ip address of the customer gateway"
  type        = string
  default     = "***********"
}

variable "customer_gateway_ip_1" {
  description = "The first customer gateway IP address"
  type        = string
}

variable "customer_gateway_ip_2" {
  description = "The second customer gateway IP address"
  type        = string
}

variable "bgp_asn" {
  description = "bgp asn of the customer gateway"
  type        = number
  default     = 65000
}

variable "vpn_static_routes_only" {
  description = "whether to use static routes only"
  type        = bool
  default     = true
}

variable "vpn_destination_cidr_blocks" {
  description = "list of destination cidr blocks for static routes"
  type        = list(string)
  default     = []
}

variable "vpn_alarm_actions" {
  description = "list of arns to notify when alarm triggers"
  type        = list(string)
  default     = []
}

variable "vpn_ok_actions" {
  description = "list of arns to notify when alarm returns to ok state"
  type        = list(string)
  default     = []
}
