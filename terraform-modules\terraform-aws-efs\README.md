# terraform-module tempalte

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_efs_backup_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/efs_backup_policy) | resource |
| [aws_efs_file_system.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/efs_file_system) | resource |
| [aws_efs_mount_target.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/efs_mount_target) | resource |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_subnet.subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnet) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_backup_policy_status"></a> [backup\_policy\_status](#input\_backup\_policy\_status) | Backup policy status for the EFS file system. Allowed values: ENABLED, DISABLED | `string` | `"ENABLED"` | no |
| <a name="input_component"></a> [component](#input\_component) | The name of the component the file system is created for | `string` | n/a | yes |
| <a name="input_creation_token"></a> [creation\_token](#input\_creation\_token) | A unique name (a maximum of 64 characters are allowed) used as reference when creating the Elastic File System to ensure idempotent file system creation. By default generated by Terraform | `string` | `null` | no |
| <a name="input_encrypted"></a> [encrypted](#input\_encrypted) | If `true`, the disk will be encrypted | `bool` | `false` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment stage | `string` | n/a | yes |
| <a name="input_kms_key_arn"></a> [kms\_key\_arn](#input\_kms\_key\_arn) | The ARN for the KMS encryption key. When specifying `kms_key_arn`, encrypted needs to be set to `true` | `string` | `null` | no |
| <a name="input_mount_targets"></a> [mount\_targets](#input\_mount\_targets) | List of subnet IDs to create mount targets in | `list(string)` | `[]` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | The VPC ID where the security group will be created | `string` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_efs_mount_target_dns_names"></a> [efs\_mount\_target\_dns\_names](#output\_efs\_mount\_target\_dns\_names) | List of EFS mount target DNS names (one per subnet) |
| <a name="output_efs_mount_target_ids"></a> [efs\_mount\_target\_ids](#output\_efs\_mount\_target\_ids) | List of EFS mount target IDs (one per subnet) |
| <a name="output_efs_mount_target_network_interface_ids"></a> [efs\_mount\_target\_network\_interface\_ids](#output\_efs\_mount\_target\_network\_interface\_ids) | List of mount target network interface IDs |
| <a name="output_efs_mount_targets"></a> [efs\_mount\_targets](#output\_efs\_mount\_targets) | Map of mount targets created and their attributes |
| <a name="output_this_efs_file_system_arn"></a> [this\_efs\_file\_system\_arn](#output\_this\_efs\_file\_system\_arn) | Amazon Resource Name of the file system |
| <a name="output_this_efs_file_system_dns_name"></a> [this\_efs\_file\_system\_dns\_name](#output\_this\_efs\_file\_system\_dns\_name) | The DNS name for the filesystem per [documented convention](http://docs.aws.amazon.com/efs/latest/ug/mounting-fs-mount-cmd-dns-name.html) |
| <a name="output_this_efs_file_system_id"></a> [this\_efs\_file\_system\_id](#output\_this\_efs\_file\_system\_id) | The ID that identifies the file system (e.g., `fs-ccfc0d65`) |
| <a name="output_this_efs_file_system_size_in_bytes"></a> [this\_efs\_file\_system\_size\_in\_bytes](#output\_this\_efs\_file\_system\_size\_in\_bytes) | The latest known metered size (in bytes) of data stored in the file system, the value is not the exact size that the file system was at any point in time |
| <a name="output_this_security_group_arn"></a> [this\_security\_group\_arn](#output\_this\_security\_group\_arn) | ARN of the security group |
| <a name="output_this_security_group_id"></a> [this\_security\_group\_id](#output\_this\_security\_group\_id) | ID of the security group |
<!-- END_TF_DOCS -->
