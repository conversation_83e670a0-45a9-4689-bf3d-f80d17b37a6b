output "this_vpc_endpoint_id" {
  description = "ID of the created VPC endpoint"
  value       = try(aws_vpc_endpoint.this.id, "")
}

output "this_vpc_endpoint_arn" {
  description = "ARN of the created VPC endpoint"
  value       = try(aws_vpc_endpoint.this.arn, "")
}

output "this_security_group_id" {
  description = "ID of the security group created for the VPC endpoint (only for Interface endpoints)"
  value       = try(aws_security_group.this[0].id, null)
}

output "subnet_associations" {
  description = "List of subnet associations created for Interface endpoints"
  value       = try([for sa in aws_vpc_endpoint_subnet_association.this : sa.id], [])
}

output "route_table_associations" {
  description = "List of route table associations created for Gateway endpoints"
  value       = try([for rta in aws_vpc_endpoint_route_table_association.this : rta.id], [])
}
