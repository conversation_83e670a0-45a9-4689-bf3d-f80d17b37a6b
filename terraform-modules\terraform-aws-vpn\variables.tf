variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "vpc_id" {
  description = "id of the vpc to attach the vpn gateway to"
  type        = string
}

variable "route_table_id" {
  description = "id of the route table where vpn routes will be added"
  type        = string
}

variable "customer_gateway_ip" {
  description = "ip address of the customer gateway"
  type        = string

  validation {
    condition     = can(regex("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$", var.customer_gateway_ip))
    error_message = "The customer_gateway_ip value must be a valid IPv4 address."
  }
}

variable "bgp_asn" {
  description = "bgp asn of the customer gateway"
  type        = number
  default     = 65000

  validation {
    condition     = var.bgp_asn >= 1 && var.bgp_asn <= 4294967295
    error_message = "The bgp_asn value must be between 1 and 4294967295."
  }
}

variable "static_routes_only" {
  description = "whether to use static routes only"
  type        = bool
  default     = true
}

variable "destination_cidr_blocks" {
  description = "list of destination cidr blocks for static routes"
  type        = list(string)
  default     = []

  validation {
    condition     = alltrue([for cidr in var.destination_cidr_blocks : can(regex("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}/(?:[0-9]|[1-2][0-9]|3[0-2])$", cidr))])
    error_message = "Each destination_cidr_blocks value must be a valid IPv4 CIDR block."
  }
}

variable "enable_route_propagation" {
  description = "whether to enable route propagation for the vpn gateway"
  type        = bool
  default     = true
}

# VPN Tunnel Configuration
variable "tunnel1_inside_ipv6_cidr" {
  description = "cidr block of the inside ip addresses for the first vpn tunnel"
  type        = string
  default     = null

  validation {
    condition     = var.tunnel1_inside_ipv6_cidr == null || can(regex("^[0-9a-fA-F:]+/[0-9]{1,3}$", var.tunnel1_inside_ipv6_cidr))
    error_message = "The tunnel1_inside_ipv6_cidr value must be a valid IPv6 CIDR block or null."
  }
}

variable "tunnel2_inside_ipv6_cidr" {
  description = "cidr block of the inside ip addresses for the second vpn tunnel"
  type        = string
  default     = null

  validation {
    condition     = var.tunnel2_inside_ipv6_cidr == null || can(regex("^[0-9a-fA-F:]+/[0-9]{1,3}$", var.tunnel2_inside_ipv6_cidr))
    error_message = "The tunnel2_inside_ipv6_cidr value must be a valid IPv6 CIDR block or null."
  }
}

# Phase 1 Configuration
variable "tunnel1_phase1_dh_group_numbers" {
  description = "list of diffie-hellman group numbers for the first vpn tunnel phase 1"
  type        = list(number)
  default     = [2, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]

  validation {
    condition     = alltrue([for group in var.tunnel1_phase1_dh_group_numbers : contains([2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], group)])
    error_message = "Each tunnel1_phase1_dh_group_numbers value must be one of: 2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24."
  }
}

variable "tunnel1_phase1_encryption_algorithms" {
  description = "list of encryption algorithms for the first vpn tunnel phase 1"
  type        = list(string)
  default     = ["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"]

  validation {
    condition     = alltrue([for algo in var.tunnel1_phase1_encryption_algorithms : contains(["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"], algo)])
    error_message = "Each tunnel1_phase1_encryption_algorithms value must be one of: AES128, AES256, AES128-GCM-16, AES256-GCM-16."
  }
}

variable "tunnel1_phase1_integrity_algorithms" {
  description = "list of integrity algorithms for the first vpn tunnel phase 1"
  type        = list(string)
  default     = ["SHA2-256", "SHA2-384", "SHA2-512"]

  validation {
    condition     = alltrue([for algo in var.tunnel1_phase1_integrity_algorithms : contains(["SHA2-256", "SHA2-384", "SHA2-512"], algo)])
    error_message = "Each tunnel1_phase1_integrity_algorithms value must be one of: SHA2-256, SHA2-384, SHA2-512."
  }
}

variable "tunnel1_phase1_lifetime_seconds" {
  description = "lifetime for phase 1 of the ike negotiation for the first vpn tunnel"
  type        = number
  default     = 28800

  validation {
    condition     = var.tunnel1_phase1_lifetime_seconds >= 900 && var.tunnel1_phase1_lifetime_seconds <= 28800
    error_message = "The tunnel1_phase1_lifetime_seconds value must be between 900 and 28800."
  }
}

# Phase 2 Configuration
variable "tunnel1_phase2_dh_group_numbers" {
  description = "list of diffie-hellman group numbers for the first vpn tunnel phase 2"
  type        = list(number)
  default     = [2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]

  validation {
    condition     = alltrue([for group in var.tunnel1_phase2_dh_group_numbers : contains([2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], group)])
    error_message = "Each tunnel1_phase2_dh_group_numbers value must be one of: 2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24."
  }
}

variable "tunnel1_phase2_encryption_algorithms" {
  description = "list of encryption algorithms for the first vpn tunnel phase 2"
  type        = list(string)
  default     = ["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"]

  validation {
    condition     = alltrue([for algo in var.tunnel1_phase2_encryption_algorithms : contains(["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"], algo)])
    error_message = "Each tunnel1_phase2_encryption_algorithms value must be one of: AES128, AES256, AES128-GCM-16, AES256-GCM-16."
  }
}

variable "tunnel1_phase2_integrity_algorithms" {
  description = "list of integrity algorithms for the first vpn tunnel phase 2"
  type        = list(string)
  default     = ["SHA2-256", "SHA2-384", "SHA2-512"]

  validation {
    condition     = alltrue([for algo in var.tunnel1_phase2_integrity_algorithms : contains(["SHA2-256", "SHA2-384", "SHA2-512"], algo)])
    error_message = "Each tunnel1_phase2_integrity_algorithms value must be one of: SHA2-256, SHA2-384, SHA2-512."
  }
}

variable "tunnel1_phase2_lifetime_seconds" {
  description = "lifetime for phase 2 of the ike negotiation for the first vpn tunnel"
  type        = number
  default     = 3600

  validation {
    condition     = var.tunnel1_phase2_lifetime_seconds >= 900 && var.tunnel1_phase2_lifetime_seconds <= 3600
    error_message = "The tunnel1_phase2_lifetime_seconds value must be between 900 and 3600."
  }
}

# Additional Tunnel 1 Settings
variable "tunnel1_rekey_fuzz_percentage" {
  description = "percentage of the rekey window for the first vpn tunnel"
  type        = number
  default     = 100

  validation {
    condition     = var.tunnel1_rekey_fuzz_percentage >= 0 && var.tunnel1_rekey_fuzz_percentage <= 100
    error_message = "The tunnel1_rekey_fuzz_percentage value must be between 0 and 100."
  }
}

variable "tunnel1_rekey_margin_time_seconds" {
  description = "margin time, in seconds, before the phase 2 lifetime expires for the first vpn tunnel"
  type        = number
  default     = 540

  validation {
    condition     = var.tunnel1_rekey_margin_time_seconds >= 60 && var.tunnel1_rekey_margin_time_seconds <= 1800
    error_message = "The tunnel1_rekey_margin_time_seconds value must be between 60 and 1800."
  }
}

variable "tunnel1_replay_window_size" {
  description = "number of packets in an ike replay window for the first vpn tunnel"
  type        = number
  default     = 1024

  validation {
    condition     = var.tunnel1_replay_window_size >= 64 && var.tunnel1_replay_window_size <= 2048
    error_message = "The tunnel1_replay_window_size value must be between 64 and 2048."
  }
}

variable "tunnel1_startup_action" {
  description = "action to take when establishing the tunnel for the first vpn connection"
  type        = string
  default     = "add"

  validation {
    condition     = contains(["add", "start"], var.tunnel1_startup_action)
    error_message = "The tunnel1_startup_action value must be either 'add' or 'start'."
  }
}

# Tunnel 2 Configuration (same as Tunnel 1)
variable "tunnel2_phase1_dh_group_numbers" {
  description = "list of diffie-hellman group numbers for the second vpn tunnel phase 1"
  type        = list(number)
  default     = [2, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]

  validation {
    condition     = alltrue([for group in var.tunnel2_phase1_dh_group_numbers : contains([2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], group)])
    error_message = "Each tunnel2_phase1_dh_group_numbers value must be one of: 2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24."
  }
}

variable "tunnel2_phase1_encryption_algorithms" {
  description = "list of encryption algorithms for the second vpn tunnel phase 1"
  type        = list(string)
  default     = ["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"]

  validation {
    condition     = alltrue([for algo in var.tunnel2_phase1_encryption_algorithms : contains(["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"], algo)])
    error_message = "Each tunnel2_phase1_encryption_algorithms value must be one of: AES128, AES256, AES128-GCM-16, AES256-GCM-16."
  }
}

variable "tunnel2_phase1_integrity_algorithms" {
  description = "list of integrity algorithms for the second vpn tunnel phase 1"
  type        = list(string)
  default     = ["SHA2-256", "SHA2-384", "SHA2-512"]

  validation {
    condition     = alltrue([for algo in var.tunnel2_phase1_integrity_algorithms : contains(["SHA2-256", "SHA2-384", "SHA2-512"], algo)])
    error_message = "Each tunnel2_phase1_integrity_algorithms value must be one of: SHA2-256, SHA2-384, SHA2-512."
  }
}

variable "tunnel2_phase1_lifetime_seconds" {
  description = "lifetime for phase 1 of the ike negotiation for the second vpn tunnel"
  type        = number
  default     = 28800

  validation {
    condition     = var.tunnel2_phase1_lifetime_seconds >= 900 && var.tunnel2_phase1_lifetime_seconds <= 28800
    error_message = "The tunnel2_phase1_lifetime_seconds value must be between 900 and 28800."
  }
}

variable "tunnel2_phase2_dh_group_numbers" {
  description = "list of diffie-hellman group numbers for the second vpn tunnel phase 2"
  type        = list(number)
  default     = [2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]

  validation {
    condition     = alltrue([for group in var.tunnel2_phase2_dh_group_numbers : contains([2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], group)])
    error_message = "Each tunnel2_phase2_dh_group_numbers value must be one of: 2, 5, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24."
  }
}

variable "tunnel2_phase2_encryption_algorithms" {
  description = "list of encryption algorithms for the second vpn tunnel phase 2"
  type        = list(string)
  default     = ["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"]

  validation {
    condition     = alltrue([for algo in var.tunnel2_phase2_encryption_algorithms : contains(["AES128", "AES256", "AES128-GCM-16", "AES256-GCM-16"], algo)])
    error_message = "Each tunnel2_phase2_encryption_algorithms value must be one of: AES128, AES256, AES128-GCM-16, AES256-GCM-16."
  }
}

variable "tunnel2_phase2_integrity_algorithms" {
  description = "list of integrity algorithms for the second vpn tunnel phase 2"
  type        = list(string)
  default     = ["SHA2-256", "SHA2-384", "SHA2-512"]

  validation {
    condition     = alltrue([for algo in var.tunnel2_phase2_integrity_algorithms : contains(["SHA2-256", "SHA2-384", "SHA2-512"], algo)])
    error_message = "Each tunnel2_phase2_integrity_algorithms value must be one of: SHA2-256, SHA2-384, SHA2-512."
  }
}

variable "tunnel2_phase2_lifetime_seconds" {
  description = "lifetime for phase 2 of the ike negotiation for the second vpn tunnel"
  type        = number
  default     = 3600

  validation {
    condition     = var.tunnel2_phase2_lifetime_seconds >= 900 && var.tunnel2_phase2_lifetime_seconds <= 3600
    error_message = "The tunnel2_phase2_lifetime_seconds value must be between 900 and 3600."
  }
}

variable "tunnel2_rekey_fuzz_percentage" {
  description = "percentage of the rekey window for the second vpn tunnel"
  type        = number
  default     = 100

  validation {
    condition     = var.tunnel2_rekey_fuzz_percentage >= 0 && var.tunnel2_rekey_fuzz_percentage <= 100
    error_message = "The tunnel2_rekey_fuzz_percentage value must be between 0 and 100."
  }
}

variable "tunnel2_rekey_margin_time_seconds" {
  description = "margin time, in seconds, before the phase 2 lifetime expires for the second vpn tunnel"
  type        = number
  default     = 540

  validation {
    condition     = var.tunnel2_rekey_margin_time_seconds >= 60 && var.tunnel2_rekey_margin_time_seconds <= 1800
    error_message = "The tunnel2_rekey_margin_time_seconds value must be between 60 and 1800."
  }
}

variable "tunnel2_replay_window_size" {
  description = "number of packets in an ike replay window for the second vpn tunnel"
  type        = number
  default     = 1024

  validation {
    condition     = var.tunnel2_replay_window_size >= 64 && var.tunnel2_replay_window_size <= 2048
    error_message = "The tunnel2_replay_window_size value must be between 64 and 2048."
  }
}

variable "tunnel2_startup_action" {
  description = "action to take when establishing the tunnel for the second vpn connection"
  type        = string
  default     = "add"

  validation {
    condition     = contains(["add", "start"], var.tunnel2_startup_action)
    error_message = "The tunnel2_startup_action value must be either 'add' or 'start'."
  }
}

# Monitoring Configuration
variable "alarm_actions" {
  description = "list of arns to notify when alarm triggers"
  type        = list(string)
  default     = []
}

variable "ok_actions" {
  description = "list of arns to notify when alarm returns to ok state"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "map of tags to apply to all resources"
  type        = map(string)
  default     = {}
} 