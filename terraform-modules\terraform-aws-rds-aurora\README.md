<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_db_subnet_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_subnet_group) | resource |
| [aws_iam_role.rds_enhanced_monitoring](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_rds_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster) | resource |
| [aws_rds_cluster_instance.reader](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_instance) | resource |
| [aws_rds_cluster_instance.writer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_instance) | resource |
| [aws_secretsmanager_secret_rotation.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_rotation) | resource |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_iam_policy.aws-rds-enhanced-monitoring](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy_document.monitoring_rds_assume_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_apply_immediately"></a> [apply\_immediately](#input\_apply\_immediately) | Specifies whether any cluster modifications are applied immediately, or during the next maintenance window. Default is `false` | `bool` | `true` | no |
| <a name="input_auto_minor_version_upgrade"></a> [auto\_minor\_version\_upgrade](#input\_auto\_minor\_version\_upgrade) | Indicates that minor engine upgrades will be applied automatically to the DB instance during the maintenance window. Default `true` | `bool` | `false` | no |
| <a name="input_backup_retention_period"></a> [backup\_retention\_period](#input\_backup\_retention\_period) | The days to retain backups for | `number` | `30` | no |
| <a name="input_ca_certificate_identifier"></a> [ca\_certificate\_identifier](#input\_ca\_certificate\_identifier) | Default CA used | `string` | `"rds-ca-rsa4096-g1"` | no |
| <a name="input_cluster_monitoring_interval"></a> [cluster\_monitoring\_interval](#input\_cluster\_monitoring\_interval) | Interval, in seconds, between points when Enhanced Monitoring metrics are collected for the DB cluster. To turn off collecting Enhanced Monitoring metrics, specify 0. Valid Values: 0, 1, 5, 10, 15, 30, 60 | `number` | `60` | no |
| <a name="input_cluster_performance_insights_enabled"></a> [cluster\_performance\_insights\_enabled](#input\_cluster\_performance\_insights\_enabled) | Enables Performance Insights for the RDS Cluster | `bool` | `true` | no |
| <a name="input_cluster_performance_insights_kms_key_id"></a> [cluster\_performance\_insights\_kms\_key\_id](#input\_cluster\_performance\_insights\_kms\_key\_id) | Specifies the KMS Key ID to encrypt Performance Insights data. If not specified, the default RDS KMS key will be used (aws/rds) | `string` | `null` | no |
| <a name="input_cluster_performance_insights_retention_period"></a> [cluster\_performance\_insights\_retention\_period](#input\_cluster\_performance\_insights\_retention\_period) | Specifies the amount of time to retain performance insights data for. Defaults to 7 days if Performance Insights are enabled. Valid values are 7, month * 31 (where month is a number of months from 1-23), and 731 | `number` | `7` | no |
| <a name="input_component"></a> [component](#input\_component) | Specifies component type like 'magento' | `string` | `""` | no |
| <a name="input_db_port"></a> [db\_port](#input\_db\_port) | Determines database port | `number` | `3306` | no |
| <a name="input_db_subnet_ids"></a> [db\_subnet\_ids](#input\_db\_subnet\_ids) | Subnet list - RDS Subnet Group (Minimum 2 in different AZ) | `list(string)` | n/a | yes |
| <a name="input_delete_automated_backups"></a> [delete\_automated\_backups](#input\_delete\_automated\_backups) | Specifies whether to remove automated backups immediately after the DB cluster is deleted | `bool` | `false` | no |
| <a name="input_deletion_protection"></a> [deletion\_protection](#input\_deletion\_protection) | If the DB instance should have deletion protection enabled. The database can't be deleted when this value is set to `true`. The default is `false` | `bool` | `true` | no |
| <a name="input_engine"></a> [engine](#input\_engine) | The name of the database engine to be used for this DB cluster. Defaults to `aurora`. Valid Values: `aurora`, `aurora-mysql`, `aurora-postgresql` | `string` | `"aurora-mysql"` | no |
| <a name="input_engine_version"></a> [engine\_version](#input\_engine\_version) | The database engine version. Updating this argument results in an outage | `string` | `"8.0.mysql_aurora.3.09.0"` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | environment stage | `string` | n/a | yes |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | The ARN for the KMS encryption key. | `string` | `null` | no |
| <a name="input_manage_master_user_password"></a> [manage\_master\_user\_password](#input\_manage\_master\_user\_password) | Set to true to allow RDS to manage the master user password in Secrets Manager | `bool` | `true` | no |
| <a name="input_master_password"></a> [master\_password](#input\_master\_password) | Password for the master DB user. Note that this may show up in logs, and it will be stored in the state file. Required unless `manage_master_user_password` is set to `true` | `string` | `null` | no |
| <a name="input_master_user_password_rotate_immediately"></a> [master\_user\_password\_rotate\_immediately](#input\_master\_user\_password\_rotate\_immediately) | Specifies whether to rotate the secret immediately or wait until the next scheduled rotation window. | `bool` | `null` | no |
| <a name="input_master_user_password_rotation_automatically_after_days"></a> [master\_user\_password\_rotation\_automatically\_after\_days](#input\_master\_user\_password\_rotation\_automatically\_after\_days) | Specifies the number of days between automatic scheduled rotations of the secret. Either `master_user_password_rotation_automatically_after_days` or `master_user_password_rotation_schedule_expression` must be specified | `number` | `null` | no |
| <a name="input_master_user_password_rotation_duration"></a> [master\_user\_password\_rotation\_duration](#input\_master\_user\_password\_rotation\_duration) | The length of the rotation window in hours. For example, 3h for a three hour window. | `string` | `null` | no |
| <a name="input_master_user_password_rotation_schedule_expression"></a> [master\_user\_password\_rotation\_schedule\_expression](#input\_master\_user\_password\_rotation\_schedule\_expression) | A cron() or rate() expression that defines the schedule for rotating your secret. Either `master_user_password_rotation_automatically_after_days` or `master_user_password_rotation_schedule_expression` must be specified | `string` | `null` | no |
| <a name="input_master_user_secret_kms_key_id"></a> [master\_user\_secret\_kms\_key\_id](#input\_master\_user\_secret\_kms\_key\_id) | The Amazon Web Services KMS key identifier is the key ARN, key ID, alias ARN, or alias name for the KMS key | `string` | `null` | no |
| <a name="input_master_username"></a> [master\_username](#input\_master\_username) | Username for the master DB user. Required unless `snapshot_identifier` | `string` | `null` | no |
| <a name="input_performance_insights_enabled"></a> [performance\_insights\_enabled](#input\_performance\_insights\_enabled) | Specifies whether Performance Insights is enabled or not | `bool` | `true` | no |
| <a name="input_performance_insights_kms_key_id"></a> [performance\_insights\_kms\_key\_id](#input\_performance\_insights\_kms\_key\_id) | The ARN for the KMS key to encrypt Performance Insights data | `string` | `null` | no |
| <a name="input_performance_insights_retention_period"></a> [performance\_insights\_retention\_period](#input\_performance\_insights\_retention\_period) | Amount of time in days to retain Performance Insights data. Either 7 (7 days) or 731 (2 years) | `number` | `7` | no |
| <a name="input_preferred_backup_window"></a> [preferred\_backup\_window](#input\_preferred\_backup\_window) | The daily time range during which automated backups are created if automated backups are enabled using the `backup_retention_period` parameter. Time in UTC | `string` | `"02:00-03:00"` | no |
| <a name="input_preferred_maintenance_window"></a> [preferred\_maintenance\_window](#input\_preferred\_maintenance\_window) | The weekly time range during which system maintenance can occur, in (UTC) | `string` | `"sun:05:00-sun:06:00"` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | project name | `string` | n/a | yes |
| <a name="input_publicly_accessible"></a> [publicly\_accessible](#input\_publicly\_accessible) | Determines whether instances are publicly accessible. Default `false` | `bool` | `false` | no |
| <a name="input_reader_instances"></a> [reader\_instances](#input\_reader\_instances) | Map reader instances | <pre>map(object({<br/>    instance_class = string<br/>  }))</pre> | `{}` | no |
| <a name="input_security_group_rules"></a> [security\_group\_rules](#input\_security\_group\_rules) | Map of security group rules | `map(any)` | `{}` | no |
| <a name="input_skip_final_snapshot"></a> [skip\_final\_snapshot](#input\_skip\_final\_snapshot) | Determines whether a final snapshot is created before the cluster is deleted. If true is specified, no snapshot is created | `bool` | `false` | no |
| <a name="input_storage_encrypted"></a> [storage\_encrypted](#input\_storage\_encrypted) | Specifies whether the DB cluster is encrypted. The default is `true` | `bool` | `true` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | ID of the VPC where to create security group | `string` | `""` | no |
| <a name="input_writer_instance_class"></a> [writer\_instance\_class](#input\_writer\_instance\_class) | Instance type to use at writer instance. | `string` | `"db.m7g.large"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_rds_cluster_instance_reader_arns"></a> [rds\_cluster\_instance\_reader\_arns](#output\_rds\_cluster\_instance\_reader\_arns) | Map of reader instance ARNs |
| <a name="output_rds_cluster_instance_reader_endpoints"></a> [rds\_cluster\_instance\_reader\_endpoints](#output\_rds\_cluster\_instance\_reader\_endpoints) | Map of reader instance endpoints |
| <a name="output_rds_cluster_instance_reader_ids"></a> [rds\_cluster\_instance\_reader\_ids](#output\_rds\_cluster\_instance\_reader\_ids) | Map of reader instance IDs |
| <a name="output_rds_cluster_instance_reader_instance_classes"></a> [rds\_cluster\_instance\_reader\_instance\_classes](#output\_rds\_cluster\_instance\_reader\_instance\_classes) | Map of reader instance classes |
| <a name="output_security_group_id"></a> [security\_group\_id](#output\_security\_group\_id) | Security group ID of the cluster |
| <a name="output_this_iam_role_rds_enhanced_monitoring_arn"></a> [this\_iam\_role\_rds\_enhanced\_monitoring\_arn](#output\_this\_iam\_role\_rds\_enhanced\_monitoring\_arn) | Amazon Resource Name (ARN) of enhanced monitoring role |
| <a name="output_this_iam_role_rds_enhanced_monitoring_name"></a> [this\_iam\_role\_rds\_enhanced\_monitoring\_name](#output\_this\_iam\_role\_rds\_enhanced\_monitoring\_name) | Name of the enhanced monitoring role |
| <a name="output_this_iam_role_rds_enhanced_monitoring_unique_id"></a> [this\_iam\_role\_rds\_enhanced\_monitoring\_unique\_id](#output\_this\_iam\_role\_rds\_enhanced\_monitoring\_unique\_id) | Unique string identifying the enhanced monitoring role |
| <a name="output_this_rds_cluster_arn"></a> [this\_rds\_cluster\_arn](#output\_this\_rds\_cluster\_arn) | Amazon Resource Name (ARN) of cluster |
| <a name="output_this_rds_cluster_ca_certificate_identifier"></a> [this\_rds\_cluster\_ca\_certificate\_identifier](#output\_this\_rds\_cluster\_ca\_certificate\_identifier) | CA identifier of the CA certificate used for the DB instance's server certificate |
| <a name="output_this_rds_cluster_ca_certificate_valid_till"></a> [this\_rds\_cluster\_ca\_certificate\_valid\_till](#output\_this\_rds\_cluster\_ca\_certificate\_valid\_till) | Expiration date of the DB instance’s server certificate |
| <a name="output_this_rds_cluster_endpoint"></a> [this\_rds\_cluster\_endpoint](#output\_this\_rds\_cluster\_endpoint) | Writer endpoint for the cluster |
| <a name="output_this_rds_cluster_engine_version_actual"></a> [this\_rds\_cluster\_engine\_version\_actual](#output\_this\_rds\_cluster\_engine\_version\_actual) | The running version of the cluster database |
| <a name="output_this_rds_cluster_hosted_zone_id"></a> [this\_rds\_cluster\_hosted\_zone\_id](#output\_this\_rds\_cluster\_hosted\_zone\_id) | The Route53 Hosted Zone ID of the endpoint |
| <a name="output_this_rds_cluster_id"></a> [this\_rds\_cluster\_id](#output\_this\_rds\_cluster\_id) | The RDS Cluster Identifier |
| <a name="output_this_rds_cluster_instance_writer_arn"></a> [this\_rds\_cluster\_instance\_writer\_arn](#output\_this\_rds\_cluster\_instance\_writer\_arn) | A cluster writer instance arn |
| <a name="output_this_rds_cluster_instance_writer_endpoint"></a> [this\_rds\_cluster\_instance\_writer\_endpoint](#output\_this\_rds\_cluster\_instance\_writer\_endpoint) | A cluster writer instance endpoint |
| <a name="output_this_rds_cluster_instance_writer_id"></a> [this\_rds\_cluster\_instance\_writer\_id](#output\_this\_rds\_cluster\_instance\_writer\_id) | A cluster writer instance id |
| <a name="output_this_rds_cluster_instance_writer_instance_class"></a> [this\_rds\_cluster\_instance\_writer\_instance\_class](#output\_this\_rds\_cluster\_instance\_writer\_instance\_class) | A cluster writer instance class |
| <a name="output_this_rds_cluster_master_password"></a> [this\_rds\_cluster\_master\_password](#output\_this\_rds\_cluster\_master\_password) | The database master password |
| <a name="output_this_rds_cluster_master_user_secret"></a> [this\_rds\_cluster\_master\_user\_secret](#output\_this\_rds\_cluster\_master\_user\_secret) | The generated database master user secret |
| <a name="output_this_rds_cluster_master_username"></a> [this\_rds\_cluster\_master\_username](#output\_this\_rds\_cluster\_master\_username) | The database master username |
| <a name="output_this_rds_cluster_port"></a> [this\_rds\_cluster\_port](#output\_this\_rds\_cluster\_port) | The database port |
| <a name="output_this_rds_cluster_reader_endpoint"></a> [this\_rds\_cluster\_reader\_endpoint](#output\_this\_rds\_cluster\_reader\_endpoint) | A read-only endpoint for the cluster, automatically load-balanced across replicas |
| <a name="output_this_secretsmanager_secret_rotation_rotation_enabled"></a> [this\_secretsmanager\_secret\_rotation\_rotation\_enabled](#output\_this\_secretsmanager\_secret\_rotation\_rotation\_enabled) | Specifies whether automatic rotation is enabled |
<!-- END_TF_DOCS -->