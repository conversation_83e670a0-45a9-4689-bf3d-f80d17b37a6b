output "this_vpc_id" {
  description = "VPC ID"
  value       = try(aws_vpc.this.id, "")
}

output "this_vpc_name" {
  description = "Name of the VPC"
  value       = try(aws_vpc.this.tags.Name, "")
}

output "public_subnet_ids" {
  description = "IDs of public subnets"
  value       = try(values(aws_subnet.public)[*].id, [])
}

output "public_subnet_availability_zones" {
  description = "Availability Zones of public subnets"
  value       = try(values(aws_subnet.public)[*].availability_zone, [])
}

output "public_subnet_names" {
  description = "Names of public subnets"
  value       = try(values(aws_subnet.public)[*].tags.Name, [])
}

output "private_subnet_ids" {
  description = "IDs of private subnets"
  value       = try(values(aws_subnet.private)[*].id, [])
}

output "private_subnet_availability_zones" {
  description = "Availability Zones of private subnets"
  value       = try(values(aws_subnet.private)[*].availability_zone, [])
}

output "private_subnet_names" {
  description = "Names of private subnets"
  value       = try(values(aws_subnet.private)[*].tags.Name, [])
}

output "protected_subnet_ids" {
  description = "IDs of protected subnets"
  value       = try(values(aws_subnet.protected)[*].id, [])
}

output "protected_subnet_availability_zones" {
  description = "Availability Zones of protected subnets"
  value       = try(values(aws_subnet.protected)[*].availability_zone, [])
}

output "protected_subnet_names" {
  description = "Names of protected subnets"
  value       = try(values(aws_subnet.protected)[*].tags.Name, [])
}

output "this_internet_gateway_id" {
  description = "ID of internet gateway"
  value       = try(aws_internet_gateway.this.id, "")
}

output "this_internet_gateway_name" {
  description = "Name of internet gateway"
  value       = try(aws_internet_gateway.this.tags.Name, "")
}

output "nat_gateway_ids" {
  description = "IDs of NAT Gateways"
  value       = try(aws_nat_gateway.this[*].id, [])
}

output "nat_gateway_names" {
  description = "Names of NAT Gateways"
  value       = try(aws_nat_gateway.this[*].tags.Name, [])
}

output "public_route_table_id" {
  description = "ID of public route table"
  value       = try(aws_route_table.public.id, "")
}

output "public_route_table_name" {
  description = "Name of public route table"
  value       = try(aws_route_table.public.tags.Name, "")
}

output "private_route_table_ids" {
  description = "IDs of private route tables"
  value       = try(aws_route_table.private[*].id, [])
}

output "private_route_table_names" {
  description = "Names of private route tables"
  value       = try(aws_route_table.private[*].tags.Name, [])
}

output "protected_route_table_id" {
  description = "ID of protected route table"
  value       = try(aws_route_table.protected.id, "")
}

output "protected_route_table_name" {
  description = "Name of protected route table"
  value       = try(aws_route_table.protected.tags.Name, "")
}
