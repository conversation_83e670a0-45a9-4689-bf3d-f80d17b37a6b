################################################################################
# RDS Proxy
################################################################################
variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "db_proxy_name" {
  description = "The name of the RDS Proxy"
  type        = string
  default     = "rdsproxy"
}

variable "db_proxy_engine_family" {
  description = "The kinds of databases that the proxy can connect to"
  type        = string
  default     = "MYSQL"
}

variable "db_proxy_debug_logging" {
  description = "Whether the proxy includes detailed information about SQL statements in its logs"
  type        = bool
  default     = false
}

variable "db_proxy_idle_client_timeout" {
  description = "The number of seconds that a connection to the proxy can be idle before the proxy closes the connection"
  type        = number
  default     = 2000
}

variable "db_proxy_require_tls" {
  description = "Whether the proxy requires TLS connections"
  type        = bool
  default     = true
}

variable "vpc_subnet_ids" {
  description = "List of subnet IDs for Interface endpoints"
  type        = set(string)
}

variable "vpc_security_group_ids" {
  description = "List of security group IDs for the RDS Proxy"
  type        = set(string)
}

variable "auth" {
  type = list(object({
    auth_scheme = string
    description = string
    iam_auth    = string
    secret_arn  = string
  }))
  description = "Configuration blocks with authorization mechanisms to connect to the associated database instances or clusters"
}

# Proxy Default Target Group
variable "connection_borrow_timeout" {
  description = "The number of seconds for a proxy to wait for a connection to become available in the connection pool"
  type        = number
  default     = 120
}

variable "init_query" {
  description = "One or more SQL statements for the proxy to run when opening each new database connection"
  type        = string
  default     = ""
}

variable "max_connections_percent" {
  description = "The maximum size of the connection pool for each target in a target group"
  type        = number
  default     = 100
}

variable "max_idle_connections_percent" {
  description = "Controls how actively the proxy closes idle database connections in the connection pool"
  type        = number
  default     = 50
}

variable "session_pinning_filters" {
  description = "Each item in the list represents a class of SQL operations that normally cause all later statements in a session using a proxy to be pinned to the same underlying database connection"
  type        = list(string)
  default     = []
}

# Proxy Target
variable "db_instance_identifier" {
  type        = string
  default     = null
  description = "DB instance identifier. Either `db_instance_identifier` or `db_cluster_identifier` should be specified and both should not be specified together"
}

variable "db_cluster_identifier" {
  type        = string
  default     = null
  description = "DB cluster identifier. Either `db_instance_identifier` or `db_cluster_identifier` should be specified and both should not be specified together"
}

################################################################################
# IAM Role
################################################################################

variable "kms_key_arn" {
  description = "KMS Key ARN to allow access to decrypt SecretsManager secrets"
  type        = string
  default     = ""
}
