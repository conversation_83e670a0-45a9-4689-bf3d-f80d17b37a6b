variable "program" {
  description = "The name of the overarching program or initiative"
  type        = string
  default     = "evolution"
}

variable "project" {
  description = "The name of the specific project within the program"
  type        = string
  default     = "axon"
}

variable "environment" {
  description = "The deployment environment (e.g., dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "region" {
  description = "The AWS region where resources will be created"
  type        = string
  default     = "us-east-2"
}

variable "aws_profile" {
  description = "The AWS profile to use for authentication"
  type        = string
  default     = "terraform"
}

variable "adapter_api_image" {
  description = "The Docker image for the Axon adapter API (full URI or will be prefixed with ECR repo URL)"
  type        = string
  default     = "latest"
}

variable "adapter_api_cpu" {
  description = "The CPU units for the Axon adapter API task"
  type        = number
  default     = 2048
}

variable "adapter_api_memory" {
  description = "The memory (in MB) for the Axon adapter API task"
  type        = number
  default     = 4096
}

variable "adapter_api_desired_count" {
  description = "The desired number of Axon adapter API tasks"
  type        = number
  default     = 1
}

variable "enable_alb" {
  description = "Whether to create an Application Load Balancer for the service"
  type        = bool
  default     = false
}

