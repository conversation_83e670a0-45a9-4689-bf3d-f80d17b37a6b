# terraform-aws-alb-listener

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_lb_listener.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener_rule.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener_rule) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_acm_certificate_arn"></a> [acm\_certificate\_arn](#input\_acm\_certificate\_arn) | ARN of the ACM Certificate | `string` | n/a | yes |
| <a name="input_alpn_policy"></a> [alpn\_policy](#input\_alpn\_policy) | Name of the Application-Layer Protocol Negotiation (ALPN) policy. Can be set if protocol is TLS | `string` | `"HTTP2Preferred"` | no |
| <a name="input_authenticate_cognito"></a> [authenticate\_cognito](#input\_authenticate\_cognito) | Configuration block for using Amazon Cognito to authenticate users. Specify only when type is authenticate-cognito | <pre>object({<br/>    authentication_request_extra_params = map(string)<br/>    on_unauthenticated_request          = string<br/>    scope                               = set(string)<br/>    session_cookie_name                 = string<br/>    session_timeout                     = number<br/>    user_pool_arn                       = string<br/>    user_pool_client_id                 = string<br/>    user_pool_domain                    = string<br/>    order                               = number<br/>  })</pre> | n/a | yes |
| <a name="input_authenticate_oidc"></a> [authenticate\_oidc](#input\_authenticate\_oidc) | Configuration block for an identity provider that is compliant with OpenID Connect (OIDC). Specify only when type is authenticate-oidc | <pre>object({<br/>    authentication_request_extra_params = map(string)<br/>    on_unauthenticated_request          = string<br/>    scope                               = set(string)<br/>    session_cookie_name                 = string<br/>    session_timeout                     = string<br/>    authorization_endpoint              = string<br/>    client_id                           = string<br/>    client_secret                       = string<br/>    issuer                              = string<br/>    token_endpoint                      = string<br/>    user_info_endpoint                  = string<br/>    order                               = number<br/>  })</pre> | n/a | yes |
| <a name="input_fixed_response"></a> [fixed\_response](#input\_fixed\_response) | Information for creating an action that returns a custom HTTP response. Required if type is fixed-response | <pre>object({<br/>    content_type = string<br/>    message_body = string<br/>    status_code  = number<br/>  })</pre> | n/a | yes |
| <a name="input_forward"></a> [forward](#input\_forward) | Configuration block for creating an action that distributes requests among one or more target groups. Specify only if type is forward | <pre>object({<br/>    arn    = string<br/>    weight = number<br/>    order  = number<br/><br/>    stickiness = object({<br/>      duration = number<br/>      enabled  = bool<br/>    })<br/>  })</pre> | n/a | yes |
| <a name="input_lb_arn"></a> [lb\_arn](#input\_lb\_arn) | ARN of the LB | `string` | n/a | yes |
| <a name="input_listener_rules"></a> [listener\_rules](#input\_listener\_rules) | Amazon Load Balancer listener rule configuration block. You can attach several rules to one listener | <pre>object({<br/>    priority = number<br/>    actions = object({<br/>      type                                = string<br/>      order                               = number<br/>      authentication_request_extra_params = map(string)<br/>      on_unauthenticated_request          = string<br/>      scope                               = set(string)<br/>      session_cookie_name                 = string<br/>      session_timeout                     = string<br/>      authorization_endpoint              = string<br/>      status_code                         = number<br/>      client_id                           = string<br/>      client_secret                       = string<br/>      issuer                              = string<br/>      token_endpoint                      = string<br/>      user_info_endpoint                  = string<br/>      user_pool_arn                       = string<br/>      user_pool_client_id                 = string<br/>      user_pool_domain                    = string<br/>      host                                = string<br/>      path                                = string<br/>      port                                = number<br/>      protocol                            = string<br/>      query                               = string<br/>      content_type                        = string<br/>      message_body                        = string<br/>      target_group_arn                    = string<br/>      arn                                 = string<br/>      weight                              = string<br/><br/>      stickiness = object({<br/>        duration = number<br/>        enabled  = bool<br/>      })<br/><br/>      condition = object({<br/>        host_header = list(string)<br/><br/>        http_header = object({<br/>          http_header_name = string<br/>          values           = list(string)<br/>        })<br/><br/>        http_request_method = object({<br/>          values = list(string)<br/>        })<br/><br/>        path_pattern = object({<br/>          values = list(string)<br/>        })<br/><br/>        query_string = map(string)<br/>        source_ip = object({<br/>          values = list(string)<br/>        })<br/>      })<br/>    })<br/>  })</pre> | n/a | yes |
| <a name="input_protocol"></a> [protocol](#input\_protocol) | Protocol for connections from clients to the load balancer | `string` | n/a | yes |
| <a name="input_redirect"></a> [redirect](#input\_redirect) | Configuration block for creating a redirect action. Required if type is redirect | <pre>object({<br/>    host     = string<br/>    path     = string<br/>    port     = number<br/>    protocol = string<br/>    query    = string<br/>    status   = string<br/>  })</pre> | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_issuer_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_issuer\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_issuer\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Issuer HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_leaf_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_leaf\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_leaf\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Leaf HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_serial\_number\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_serial\_number\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Serial-Number HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_subject_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_subject\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_subject\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Subject HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_mtls_clientcert_validity_header_name"></a> [routing\_http\_request\_x\_amzn\_mtls\_clientcert\_validity\_header\_name](#input\_routing\_http\_request\_x\_amzn\_mtls\_clientcert\_validity\_header\_name) | Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Validity HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_tls_cipher_suite_header_name"></a> [routing\_http\_request\_x\_amzn\_tls\_cipher\_suite\_header\_name](#input\_routing\_http\_request\_x\_amzn\_tls\_cipher\_suite\_header\_name) | Enables you to modify the header name of the X-Amzn-Tls-Cipher-Suite HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_request_x_amzn_tls_version_header_name"></a> [routing\_http\_request\_x\_amzn\_tls\_version\_header\_name](#input\_routing\_http\_request\_x\_amzn\_tls\_version\_header\_name) | Enables you to modify the header name of the X-Amzn-Tls-Version HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_allow_credentials_header_value"></a> [routing\_http\_response\_access\_control\_allow\_credentials\_header\_value](#input\_routing\_http\_response\_access\_control\_allow\_credentials\_header\_value) | Specifies which headers the browser can expose to the requesting client. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_allow_headers_header_value"></a> [routing\_http\_response\_access\_control\_allow\_headers\_header\_value](#input\_routing\_http\_response\_access\_control\_allow\_headers\_header\_value) | Specifies which headers can be used during the request. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_allow_methods_header_value"></a> [routing\_http\_response\_access\_control\_allow\_methods\_header\_value](#input\_routing\_http\_response\_access\_control\_allow\_methods\_header\_value) | Set which HTTP methods are allowed when accessing the server from a different origin. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_allow_origin_header_value"></a> [routing\_http\_response\_access\_control\_allow\_origin\_header\_value](#input\_routing\_http\_response\_access\_control\_allow\_origin\_header\_value) | Specifies which origins are allowed to access the server. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_expose_headers_header_value"></a> [routing\_http\_response\_access\_control\_expose\_headers\_header\_value](#input\_routing\_http\_response\_access\_control\_expose\_headers\_header\_value) | Specifies whether the browser should include credentials such as cookies or authentication when making requests. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_access_control_max_age_header_value"></a> [routing\_http\_response\_access\_control\_max\_age\_header\_value](#input\_routing\_http\_response\_access\_control\_max\_age\_header\_value) | Specifies how long the results of a preflight request can be cached, in seconds. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_content_security_policy_header_value"></a> [routing\_http\_response\_content\_security\_policy\_header\_value](#input\_routing\_http\_response\_content\_security\_policy\_header\_value) | Specifies restrictions enforced by the browser to help minimize the risk of certain types of security threats. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_server_enabled"></a> [routing\_http\_response\_server\_enabled](#input\_routing\_http\_response\_server\_enabled) | Enables you to allow or remove the HTTP response server header. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `bool` | n/a | yes |
| <a name="input_routing_http_response_strict_transport_security_header_value"></a> [routing\_http\_response\_strict\_transport\_security\_header\_value](#input\_routing\_http\_response\_strict\_transport\_security\_header\_value) | Informs browsers that the site should only be accessed using HTTPS, and that any future attempts to access it using HTTP should automatically be converted to HTTPS | `string` | n/a | yes |
| <a name="input_routing_http_response_x_content_type_options_header_value"></a> [routing\_http\_response\_x\_content\_type\_options\_header\_value](#input\_routing\_http\_response\_x\_content\_type\_options\_header\_value) | Indicates whether the MIME types advertised in the Content-Type headers should be followed and not be changed. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_routing_http_response_x_frame_options_header_value"></a> [routing\_http\_response\_x\_frame\_options\_header\_value](#input\_routing\_http\_response\_x\_frame\_options\_header\_value) | Indicates whether the browser is allowed to render a page in a frame, iframe, embed or object. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers | `string` | n/a | yes |
| <a name="input_single_target_group_forward"></a> [single\_target\_group\_forward](#input\_single\_target\_group\_forward) | Specify only if you want to route to a single target group. Used together with forward type | <pre>object({<br/>    target_group_arn = string<br/>    order            = number<br/>  })</pre> | n/a | yes |
| <a name="input_ssl_policy"></a> [ssl\_policy](#input\_ssl\_policy) | Name of the SSL Policy for the listener. Required for HTTPs and TLS protocol | `string` | `"ELBSecurityPolicy-TLS13-1-2-2021-06"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_this_lb_listener_arn"></a> [this\_lb\_listener\_arn](#output\_this\_lb\_listener\_arn) | ARN of the load balancer listener |
| <a name="output_this_lb_listener_rule_arn"></a> [this\_lb\_listener\_rule\_arn](#output\_this\_lb\_listener\_rule\_arn) | Map of ARN of the load balancer listener rules |
| <a name="output_this_lb_listener_rule_id"></a> [this\_lb\_listener\_rule\_id](#output\_this\_lb\_listener\_rule\_id) | Map of ID of the load balancer listener rules |
<!-- END_TF_DOCS -->
