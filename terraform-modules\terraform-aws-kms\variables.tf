variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "component_name" {
  type        = string
  description = "Name of the component the key is used for."
}

variable "description" {
  type        = string
  description = "(Optional) The description of the key as viewed in AWS console."
  default     = null
}

variable "key_usage" {
  type        = string
  description = "(Optional) Specifies the intended use of the key. Valid values: ENCRYPT_DECRYPT, SIGN_VERIFY, or GENERATE_VERIFY_MAC. Defaults to ENCRYPT_DECRYPT."
  default     = "ENCRYPT_DECRYPT"

  validation {
    condition     = contains(["ENCRYPT_DECRYPT", "SIGN_VERIFY", "GENERATE_VERIFY_MAC"], var.key_usage)
    error_message = "Valid values for key_usage are: ENCRYPT_DECRYPT, SIGN_VERIFY, or GENERATE_VERIFY_MAC."
  }
}

variable "policy" {
  type = object({
    Version = optional(string)
    Statement = optional(list(object({
      Action    = list(string)
      Effect    = string
      Principal = map(any)
      Condition = optional(map(map(string)))
      Resource  = list(string)
      Sid       = string
    })))
  })
  description = "(Optional) A valid policy JSON document. Although this is a key policy, not an IAM policy, an aws_iam_policy_document, in the form that designates a principal, can be used. For more information about building policy documents with Terraform, see the AWS IAM Policy Document Guide."
  default     = {}
}

variable "deletion_window_in_days" {
  type        = number
  description = "(Optional) The waiting period, specified in number of days. After the waiting period ends, AWS KMS deletes the KMS key. If you specify a value, it must be between 7 and 30, inclusive."
  default     = 30

  validation {
    condition     = var.deletion_window_in_days >= 7 && var.deletion_window_in_days <= 30
    error_message = "The deletion_window_in_days value must be between 7 and 30, inclusive."
  }
}

variable "is_enabled" {
  type        = bool
  description = "(Optional) Specifies whether the key is enabled. Defaults to true."
  default     = true
}

variable "enable_key_rotation" {
  type        = bool
  description = "(Optional) Specifies whether key rotation is enabled. Defaults to false."
  default     = false
}

variable "multi_region" {
  type        = bool
  description = "(Optional) Indicates whether the KMS key is a multi-Region (true) or regional (false) key. Defaults to false."
  default     = false
}
