variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "cidr_block" {
  description = "vpc cidr block (X.X.X.X/X notation)"
  type        = string
}

variable "public_subnets" {
  description = "List of public subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "private_subnets" {
  description = "List of private subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "protected_subnets" {
  description = "List of protected subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "enable_dns_support" {
  description = "Determines whether the VPC supports DNS resolution"
  type        = bool
  default     = true
}

variable "enable_dns_hostnames" {
  description = "Determines whether Public instances in the VPC receive public DNS hostnames"
  type        = bool
  default     = true
}
