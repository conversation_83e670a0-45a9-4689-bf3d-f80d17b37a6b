#trivy:ignore:aws-elb-http-not-used
resource "aws_lb_listener" "this" {
  load_balancer_arn = var.lb_arn

  ssl_policy      = contains(["HTTPS", "TLS"], var.protocol) ? var.ssl_policy : null
  certificate_arn = contains(["HTTPS", "TLS"], var.protocol) ? var.acm_certificate_arn : null
  alpn_policy     = contains(["TLS"], var.protocol) ? var.alpn_policy : null

  dynamic "default_action" {
    for_each = try([var.authenticate_cognito], [])

    content {
      authenticate_cognito {
        authentication_request_extra_params = try(default_action.value.authentication_request_extra_params, null)
        on_unauthenticated_request          = try(default_action.value.on_unauthenticated_request, null)
        scope                               = try(default_action.value.scope, null)
        session_cookie_name                 = try(default_action.value.session_cookie_name, null)
        session_timeout                     = try(default_action.value.session_timeout, null)
        user_pool_arn                       = default_action.value.user_pool_arn
        user_pool_client_id                 = default_action.value.user_pool_client_id
        user_pool_domain                    = default_action.value.user_pool_domain
      }

      order = try(default_action.value.order, null)
      type  = "authenticate-cognito"
    }
  }

  dynamic "default_action" {
    for_each = try([var.authenticate_oidc], [])

    content {
      authenticate_oidc {
        authentication_request_extra_params = try(default_action.value.authentication_request_extra_params, null)
        on_unauthenticated_request          = try(default_action.value.on_unauthenticated_request, null)
        scope                               = try(default_action.value.scope, null)
        session_cookie_name                 = try(default_action.value.session_cookie_name, null)
        session_timeout                     = try(default_action.value.session_timeout, null)
        authorization_endpoint              = default_action.value.authorization_endpoint
        client_id                           = default_action.value.client_id
        client_secret                       = default_action.value.client_secret
        issuer                              = default_action.value.issuer
        token_endpoint                      = default_action.value.token_endpoint
        user_info_endpoint                  = default_action.value.user_info_endpoint
      }

      order = try(default_action.value.order, null)
      type  = "authenticate-oidc"
    }
  }

  dynamic "default_action" {
    for_each = try([var.fixed_response], [])

    content {
      fixed_response {
        content_type = default_action.value.content_type
        message_body = try(default_action.value.message_body, null)
        status_code  = try(default_action.value.status_code, null)
      }

      order = try(default_action.value.order, null)
      type  = "fixed-response"
    }
  }

  dynamic "default_action" {
    for_each = try([var.single_target_group_forward], [])

    content {
      order            = try(default_action.value.order, null)
      target_group_arn = try(default_action.value.target_groups, null)
      type             = "forward"
    }
  }

  dynamic "default_action" {
    for_each = try([var.forward], [])

    content {
      forward {
        dynamic "target_group" {
          for_each = try(default_action.value.target_groups, [])

          content {
            arn    = try(target_group.value.arn, null)
            weight = try(target_group.value.weight, null)
          }
        }

        dynamic "stickiness" {
          for_each = try([default_action.value.stickiness], [])

          content {
            duration = try(stickiness.value.duration, 60)
            enabled  = try(stickiness.value.enabled, null)
          }
        }
      }

      order = try(default_action.value.order, null)
      type  = "forward"
    }
  }

  dynamic "default_action" {
    for_each = try([var.redirect], [])

    content {
      order = try(default_action.value.order, null)

      redirect {
        host        = try(default_action.value.host, null)
        path        = try(default_action.value.path, null)
        port        = try(default_action.value.port, null)
        protocol    = try(default_action.value.protocol, null)
        query       = try(default_action.value.query, null)
        status_code = default_action.value.status_code
      }

      type = "redirect"
    }
  }


  routing_http_response_server_enabled                                = try(var.routing_http_response_server_enabled, null)
  routing_http_response_strict_transport_security_header_value        = try(var.routing_http_response_strict_transport_security_header_value, null)
  routing_http_response_access_control_allow_origin_header_value      = try(var.routing_http_response_access_control_allow_origin_header_value, null)
  routing_http_response_access_control_allow_methods_header_value     = try(var.routing_http_response_access_control_allow_methods_header_value, null)
  routing_http_response_access_control_allow_headers_header_value     = try(var.routing_http_response_access_control_allow_headers_header_value, null)
  routing_http_response_access_control_allow_credentials_header_value = try(var.routing_http_response_access_control_allow_credentials_header_value, null)
  routing_http_response_access_control_expose_headers_header_value    = try(var.routing_http_response_access_control_expose_headers_header_value, null)
  routing_http_response_access_control_max_age_header_value           = try(var.routing_http_response_access_control_max_age_header_value, null)
  routing_http_response_content_security_policy_header_value          = try(var.routing_http_response_content_security_policy_header_value, null)
  routing_http_response_x_content_type_options_header_value           = try(var.routing_http_response_x_content_type_options_header_value, null)
  routing_http_response_x_frame_options_header_value                  = try(var.routing_http_response_x_frame_options_header_value, null)

  routing_http_request_x_amzn_tls_version_header_name                   = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_tls_version_header_name, null) : null
  routing_http_request_x_amzn_tls_cipher_suite_header_name              = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_tls_cipher_suite_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_header_name               = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_issuer_header_name        = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_issuer_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_subject_header_name       = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_subject_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_validity_header_name      = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_validity_header_name, null) : null
  routing_http_request_x_amzn_mtls_clientcert_leaf_header_name          = contains(["HTTPS"], var.protocol) ? try(var.routing_http_request_x_amzn_mtls_clientcert_leaf_header_name, null) : null


  tags = var.tags
}

resource "aws_lb_listener_rule" "this" {
  for_each = var.listener_rules

  listener_arn = aws_lb_listener.this.arn
  priority     = try(each.value.priority, null)

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "authenticate-cognito"]

    content {
      type  = "authenticate-cognito"
      order = try(action.value.order, null)

      authenticate_cognito {
        authentication_request_extra_params = try(action.value.authentication_request_extra_params, null)
        on_unauthenticated_request          = try(action.value.on_unauthenticated_request, null)
        scope                               = try(action.value.scope, null)
        session_cookie_name                 = try(action.value.session_cookie_name, null)
        session_timeout                     = try(action.value.session_timeout, null)
        user_pool_arn                       = action.value.user_pool_arn
        user_pool_client_id                 = action.value.user_pool_client_id
        user_pool_domain                    = action.value.user_pool_domain
      }
    }
  }

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "authenticate-oidc"]

    content {
      type  = "authenticate-oidc"
      order = try(action.value.order, null)

      authenticate_oidc {
        authentication_request_extra_params = try(action.value.authentication_request_extra_params, null)
        on_unauthenticated_request          = try(action.value.on_unauthenticated_request, null)
        scope                               = try(action.value.scope, null)
        session_cookie_name                 = try(action.value.session_cookie_name, null)
        session_timeout                     = try(action.value.session_timeout, null)
        authorization_endpoint              = action.value.authorization_endpoint
        client_id                           = action.value.client_id
        client_secret                       = action.value.client_secret
        issuer                              = action.value.issuer
        token_endpoint                      = action.value.token_endpoint
        user_info_endpoint                  = action.value.user_info_endpoint
      }
    }
  }

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "redirect"]

    content {
      type  = "redirect"
      order = try(action.value.order, null)

      redirect {
        host        = try(action.value.host, null)
        path        = try(action.value.path, null)
        port        = try(action.value.port, null)
        protocol    = try(action.value.protocol, null)
        query       = try(action.value.query, null)
        status_code = action.value.status_code
      }
    }
  }

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "fixed-response"]

    content {
      type  = "fixed-response"
      order = try(action.value.order, null)

      fixed_response {
        content_type = action.value.content_type
        message_body = try(action.value.message_body, null)
        status_code  = try(action.value.status_code, null)
      }
    }
  }

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "single_target_group_forward"]

    content {
      type             = "forward"
      order            = try(action.value.order, null)
      target_group_arn = try(action.value.target_group_arn, null)
    }
  }

  dynamic "action" {
    for_each = [for action in each.value.actions : action if action.type == "forward"]

    content {
      type  = "forward"
      order = try(action.value.order, null)

      forward {
        dynamic "target_group" {
          for_each = try(action.value.target_groups, [])

          content {
            arn    = try(target_group.value.arn)
            weight = try(target_group.value.weight, null)
          }
        }

        dynamic "stickiness" {
          for_each = try([action.value.stickiness], [])

          content {
            enabled  = try(stickiness.value.enabled, null)
            duration = try(stickiness.value.duration, 60)
          }
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "host_header")]

    content {
      dynamic "host_header" {
        for_each = try([condition.value.host_header], [])

        content {
          values = host_header.value.values
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "http_header")]

    content {
      dynamic "http_header" {
        for_each = try([condition.value.http_header], [])

        content {
          http_header_name = http_header.value.http_header_name
          values           = http_header.value.values
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "http_request_method")]

    content {
      dynamic "http_request_method" {
        for_each = try([condition.value.http_request_method], [])

        content {
          values = http_request_method.value.values
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "path_pattern")]

    content {
      dynamic "path_pattern" {
        for_each = try([condition.value.path_pattern], [])

        content {
          values = path_pattern.value.values
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "query_string")]

    content {
      dynamic "query_string" {
        for_each = try(flatten([condition.value.query_string]), [])

        content {
          key   = try(query_string.value.key, null)
          value = query_string.value.value
        }
      }
    }
  }

  dynamic "condition" {
    for_each = [for condition in each.value.conditions : condition if contains(keys(condition), "source_ip")]

    content {
      dynamic "source_ip" {
        for_each = try([condition.value.source_ip], [])

        content {
          values = source_ip.value.values
        }
      }
    }
  }

  tags = var.tags
}
