resource "aws_subnet" "public" {
  for_each = { for subnet in var.public_subnets : subnet.availability_zone => subnet }

  vpc_id            = aws_vpc.this.id
  availability_zone = each.value.availability_zone
  cidr_block        = each.value.cidr_block

  tags = {
    Name = join("-", [var.program, var.environment, "subnet", "public", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }
}

resource "aws_subnet" "private" {
  for_each = { for subnet in var.private_subnets : subnet.availability_zone => subnet }

  vpc_id            = aws_vpc.this.id
  availability_zone = each.value.availability_zone
  cidr_block        = each.value.cidr_block

  tags = {
    Name = join("-", [var.program, var.environment, "subnet", "private", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }
}

resource "aws_subnet" "protected" {
  for_each = { for subnet in var.protected_subnets : subnet.availability_zone => subnet }

  vpc_id            = aws_vpc.this.id
  availability_zone = each.value.availability_zone
  cidr_block        = each.value.cidr_block

  tags = {
    Name = join("-", [var.program, var.environment, "subnet", "protected", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }
}
