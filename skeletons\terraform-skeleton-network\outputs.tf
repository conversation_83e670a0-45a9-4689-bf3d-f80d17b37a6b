output "vpc" {
  description = "VPC module outputs"
  value       = try(module.vpc, {})
}

output "ecr_api" {
  description = "ECR.API VPC Endpoint outputs"
  value       = try(module.ecr_api, {})
}

output "ecr_dkr" {
  description = "ECR.DKR VPC Endpoint outputs"
  value       = try(module.ecr_dkr, {})
}

output "vpn_gateway_id" {
  description = "id of the vpn gateway"
  value       = module.vpn.this_vpn_gateway_id
}

output "vpn_connection_id" {
  description = "id of the vpn connection"
  value       = module.vpn.this_vpn_connection_id
}

output "vpn_tunnel1_address" {
  description = "public ip address of the first vpn tunnel"
  value       = module.vpn.this_vpn_connection_tunnel1_address
}

output "vpn_tunnel2_address" {
  description = "public ip address of the second vpn tunnel"
  value       = module.vpn.this_vpn_connection_tunnel2_address
}

output "vpn_tunnel_status_alarms" {
  description = "cloudwatch alarms monitoring the vpn tunnel status"
  value       = module.vpn.this_vpn_connection_tunnel_status_alarms
}
