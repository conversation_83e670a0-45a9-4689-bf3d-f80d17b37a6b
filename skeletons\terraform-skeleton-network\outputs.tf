output "vpc" {
  description = "VPC module outputs"
  value       = try(module.vpc, {})
}

output "ecr_api" {
  description = "ECR.API VPC Endpoint outputs"
  value       = try(module.ecr_api, {})
}

output "ecr_dkr" {
  description = "ECR.DKR VPC Endpoint outputs"
  value       = try(module.ecr_dkr, {})
}

output "vpn_primary_gateway_id" {
  description = "id of the primary vpn gateway (Spectrum)"
  value       = module.vpn_primary.this_vpn_gateway_id
}

output "vpn_secondary_gateway_id" {
  description = "id of the secondary vpn gateway (Bright Speed)"
  value       = module.vpn_secondary.this_vpn_gateway_id
}

output "vpn_primary_connection_id" {
  description = "id of the primary vpn connection (Spectrum)"
  value       = module.vpn_primary.this_vpn_connection_id
}

output "vpn_secondary_connection_id" {
  description = "id of the secondary vpn connection (Bright Speed)"
  value       = module.vpn_secondary.this_vpn_connection_id
}

output "vpn_primary_tunnel1_address" {
  description = "public ip address of the primary vpn tunnel 1 (Spectrum)"
  value       = module.vpn_primary.this_vpn_connection_tunnel1_address
}

output "vpn_primary_tunnel2_address" {
  description = "public ip address of the primary vpn tunnel 2 (Spectrum)"
  value       = module.vpn_primary.this_vpn_connection_tunnel2_address
}

output "vpn_secondary_tunnel1_address" {
  description = "public ip address of the secondary vpn tunnel 1 (Bright Speed)"
  value       = module.vpn_secondary.this_vpn_connection_tunnel1_address
}

output "vpn_secondary_tunnel2_address" {
  description = "public ip address of the secondary vpn tunnel 2 (Bright Speed)"
  value       = module.vpn_secondary.this_vpn_connection_tunnel2_address
}

output "vpn_tunnel_status_alarms" {
  description = "cloudwatch alarms monitoring the vpn tunnel status"
  value = {
    primary   = module.vpn_primary.this_vpn_connection_tunnel_status_alarms
    secondary = module.vpn_secondary.this_vpn_connection_tunnel_status_alarms
  }
}
