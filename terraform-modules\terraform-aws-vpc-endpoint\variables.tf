variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC in which endpoints will be created"
  type        = string
}

variable "service_name" {
  description = "Name of the AWS service for the VPC endpoint"
  type        = string
}

variable "endpoint_type" {
  description = "Type of VPC endpoint (Interface or Gateway)"
  type        = string
  default     = "Interface"
}

variable "private_dns_enabled" {
  description = "Enable private DNS for the VPC endpoint"
  type        = bool
  default     = true
}

variable "policy" {
  description = "Policy JSON to attach to the VPC endpoint"
  type        = string
  default     = ""
}

variable "subnet_ids" {
  description = "List of subnet IDs for Interface endpoints"
  type        = list(string)
  default     = []
}

variable "route_table_ids" {
  description = "List of route table IDs for Gateway endpoints"
  type        = list(string)
  default     = []
}
