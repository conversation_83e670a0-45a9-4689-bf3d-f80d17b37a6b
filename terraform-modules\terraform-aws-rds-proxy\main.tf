# RDS Proxy
resource "aws_db_proxy" "this" {
  name                   = join("-", [var.program, var.project, var.environment, var.db_proxy_name])
  engine_family          = var.db_proxy_engine_family
  debug_logging          = var.db_proxy_debug_logging
  idle_client_timeout    = var.db_proxy_idle_client_timeout
  require_tls            = var.db_proxy_require_tls
  role_arn               = aws_iam_role.this.arn
  vpc_security_group_ids = var.vpc_security_group_ids
  vpc_subnet_ids         = var.vpc_subnet_ids

  dynamic "auth" {
    for_each = var.auth

    content {
      auth_scheme = auth.value.auth_scheme
      description = auth.value.description
      iam_auth    = auth.value.iam_auth
      secret_arn  = auth.value.secret_arn
    }
  }
}

resource "aws_db_proxy_default_target_group" "this" {
  db_proxy_name = aws_db_proxy.this.name

  connection_pool_config {
    connection_borrow_timeout    = var.connection_borrow_timeout
    init_query                   = var.init_query
    max_connections_percent      = var.max_connections_percent
    max_idle_connections_percent = var.max_idle_connections_percent
    session_pinning_filters      = var.session_pinning_filters
  }
}

resource "aws_db_proxy_target" "this" {
  db_proxy_name          = aws_db_proxy.this.name
  target_group_name      = aws_db_proxy_default_target_group.this.name
  db_instance_identifier = var.db_instance_identifier
  db_cluster_identifier  = var.db_cluster_identifier
}

resource "aws_db_proxy_endpoint" "read_write" {
  db_proxy_name          = aws_db_proxy.this.name
  db_proxy_endpoint_name = join("-", [var.program, var.project, var.environment, var.db_proxy_name, "rw"])
  vpc_subnet_ids         = var.vpc_subnet_ids
  target_role            = "READ_WRITE"
}

resource "aws_db_proxy_endpoint" "read_only" {
  db_proxy_name          = aws_db_proxy.this.name
  db_proxy_endpoint_name = join("-", [var.program, var.project, var.environment, var.db_proxy_name, "ro"])
  vpc_subnet_ids         = var.vpc_subnet_ids
  target_role            = "READ_ONLY"
}

# IAM Role
resource "aws_iam_role" "this" {
  name               = join("-", [var.program, var.project, var.environment, var.db_proxy_name, "role"])
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

resource "aws_iam_policy" "this" {
  name   = join("-", [var.program, var.project, var.environment, var.db_proxy_name, "policy"])
  policy = data.aws_iam_policy_document.decrypt_secrets.json
}

resource "aws_iam_role_policy_attachment" "this" {
  role       = aws_iam_role.this.name
  policy_arn = aws_iam_policy.this.arn
}
