variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Load balancer name"
  type        = string
}

variable "internal" {
  default     = false
  description = "If true, the LB will be internal"
  type        = bool
}

variable "load_balancer_type" {
  default     = "application"
  description = "Type of load balancer"
  type        = string
}

variable "subnets" {
  description = "List of subnet IDs to attach to the LB"
  type        = list(string)
}

variable "enable_cross_zone_load_balancing" {
  default     = true
  description = "If true, cross-zone load balancing of the load balancer will be enabled. For LB application type this option is always enabled and cannot be disabled"
  type        = bool
}

variable "enable_http2" {
  default     = true
  description = "Whether HTTP/2 is enabled in application load balancers"
  type        = bool
}

variable "enable_tls_version_and_cipher_suite_headers" {
  default     = false
  description = "Whether the two headers (x-amzn-tls-version and x-amzn-tls-cipher-suite), which contain information about the negotiated TLS version and cipher suite, are added to the client request before sending it to the target. Only valid for LB application type"
  type        = bool
}

variable "drop_invalid_header_fields" {
  default     = true
  description = "Whether HTTP headers with header fields that are not valid are removed by the load balancer (true) or routed to targets (false). Elastic Load Balancing requires that message header names contain only alphanumeric characters and hyphens. Only valid for LB application type"
  type        = bool
}

variable "idle_timeout" {
  default     = 60
  description = "Time in seconds that the connection is allowed to be idle. Only valid for LB application type"
  type        = number
}

variable "ip_address_type" {
  default     = "dualstack"
  description = "Type of IP addresses used by the subnets for your load balancer"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "tags" {
  default     = {}
  description = "A map of tags to add to all resources"
  type        = map(string)
}

variable "access_logs_bucket_id" {
  default     = null
  description = "ID of the access logs S3 bucket"
  type        = string
}
