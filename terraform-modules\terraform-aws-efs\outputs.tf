output "this_efs_file_system_arn" {
  description = "Amazon Resource Name of the file system"
  value       = try(aws_efs_file_system.this.arn, null)
}

output "this_efs_file_system_id" {
  description = "The ID that identifies the file system (e.g., `fs-ccfc0d65`)"
  value       = try(aws_efs_file_system.this.id, null)
}

output "this_efs_file_system_dns_name" {
  description = "The DNS name for the filesystem per [documented convention](http://docs.aws.amazon.com/efs/latest/ug/mounting-fs-mount-cmd-dns-name.html)"
  value       = try(aws_efs_file_system.this.dns_name, null)
}

output "this_efs_file_system_size_in_bytes" {
  description = "The latest known metered size (in bytes) of data stored in the file system, the value is not the exact size that the file system was at any point in time"
  value       = try(aws_efs_file_system.this.size_in_bytes, null)
}

output "efs_mount_target_ids" {
  description = "List of EFS mount target IDs (one per subnet)"
  value = try(
    [for mt in aws_efs_mount_target.this : mt.id],
    []
  )
}

output "efs_mount_target_dns_names" {
  description = "List of EFS mount target DNS names (one per subnet)"
  value = try(
    [for mt in aws_efs_mount_target.this : mt.dns_name],
    []
  )
}

output "efs_mount_target_network_interface_ids" {
  description = "List of mount target network interface IDs"
  value = try(
    [for mt in aws_efs_mount_target.this : mt.network_interface_id],
    []
  )
}

output "efs_mount_targets" {
  description = "Map of mount targets created and their attributes"
  value = try(
    aws_efs_mount_target.this,
    {}
  )
}

output "this_security_group_arn" {
  description = "ARN of the security group"
  value       = try(aws_security_group.this.arn, null)
}

output "this_security_group_id" {
  description = "ID of the security group"
  value       = try(aws_security_group.this.id, null)
}
