output "task_definition_arn" {
  description = "ARN of the task definition"
  value       = module.task.this_task_definition_arn
}

output "task_definition_family" {
  description = "Family of the task definition"
  value       = module.task.this_task_definition_family
}

output "task_definition_revision" {
  description = "Revision of the task definition"
  value       = module.task.this_task_definition_revision
}

output "service_id" {
  description = "ID of the ECS service"
  value       = module.service.this_ecs_service_id
}

output "service_name" {
  description = "Name of the ECS service"
  value       = module.service.this_ecs_service_name
}

output "service_cluster" {
  description = "Cluster of the ECS service"
  value       = module.service.this_ecs_service_cluster
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = try(aws_cloudwatch_log_group.this[0].name, null)
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = try(aws_cloudwatch_log_group.this[0].arn, null)
}

output "service_discovery_service_arn" {
  description = "ARN of the service discovery service"
  value       = try(aws_service_discovery_service.this[0].arn, null)
}

output "service_discovery_service_id" {
  description = "ID of the service discovery service"
  value       = try(aws_service_discovery_service.this[0].id, null)
}

output "ecr_repository_url" {
  description = "The URL of the ECR repository"
  value       = var.create_ecr_repository ? aws_ecr_repository.this[0].repository_url : null
}

output "ecr_repository_arn" {
  description = "The ARN of the ECR repository"
  value       = var.create_ecr_repository ? aws_ecr_repository.this[0].arn : null
}