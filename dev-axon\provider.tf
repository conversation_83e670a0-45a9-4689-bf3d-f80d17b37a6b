terraform {
  required_version = ">= 1.9.0"

  required_providers {
    aws = {
      source  = "registry.opentofu.org/hashicorp/aws"
      version = "~> 5.94"
    }
  }

  backend "s3" {
    bucket         = "evolution-terraform-state-dev"
    key            = "axon/terraform.tfstate"
    region         = "us-east-2"
    dynamodb_table = "evolution-terraform-state-lock-dev"
    encrypt        = true
    kms_key_id     = "alias/evolution-terraform-state-key-dev"
  }
}

provider "aws" {
  region  = var.region
  profile = "terraform"

  default_tags {
    tags = {
      Program     = var.program
      Project     = var.project
      Environment = var.environment
      Team        = "enterprise-digitalization"
    }
  }
}