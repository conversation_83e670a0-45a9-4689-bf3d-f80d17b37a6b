resource "aws_efs_file_system" "this" {
  creation_token   = var.creation_token
  performance_mode = "generalPurpose"
  encrypted        = var.encrypted # trivy:ignore:AVD-AWS-0037 sterowanie enkrypcja poprzez zmienna
  kms_key_id       = var.kms_key_arn
  throughput_mode  = "elastic"

  tags = merge(
    var.tags,
    {
      Name = join("-", [var.program, var.project, var.environment, var.component, "efs"])
    },
  )
}

resource "aws_efs_backup_policy" "this" {
  file_system_id = aws_efs_file_system.this.id

  backup_policy {
    status = var.backup_policy_status
  }
}

resource "aws_efs_mount_target" "this" {
  for_each = toset(var.mount_targets)

  file_system_id  = aws_efs_file_system.this.id
  security_groups = [aws_security_group.this.id]
  subnet_id       = each.value
}

resource "aws_security_group" "this" {
  name        = join("-", [var.program, var.project, var.environment, var.component, "efs", "sg"])
  description = "Security group for EFS access"

  revoke_rules_on_delete = true
  vpc_id                 = var.vpc_id

  tags = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group_rule" "ingress" {
  for_each = data.aws_subnet.subnets

  type              = "ingress"
  from_port         = 2049
  to_port           = 2049
  protocol          = "tcp"
  cidr_blocks       = [each.value.cidr_block]
  security_group_id = aws_security_group.this.id
  description       = "Allow NFS from ${each.value.cidr_block}"
}

#trivy:ignore:aws-vpc-no-public-egress-sgr
resource "aws_security_group_rule" "egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.this.id
  description       = "Allow all outbound traffic"
}
