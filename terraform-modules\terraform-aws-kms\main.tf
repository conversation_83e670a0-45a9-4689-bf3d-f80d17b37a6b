#trivy:ignore:AVD-AWS-0065
resource "aws_kms_key" "this" {
  description             = var.description
  key_usage               = var.key_usage
  is_enabled              = var.is_enabled
  deletion_window_in_days = var.deletion_window_in_days
  enable_key_rotation     = var.enable_key_rotation
  multi_region            = var.multi_region

  policy = (
    var.policy.Version != null && var.policy.Statement != null ?
    data.aws_iam_policy_document.custom_kms_policy[0].json :
    data.aws_iam_policy_document.default_kms_policy.json
  )

}

resource "aws_kms_alias" "this" {
  name          = "alias/${var.program}-${var.project}-${var.environment}-${var.component_name}"
  target_key_id = aws_kms_key.this.key_id
}
