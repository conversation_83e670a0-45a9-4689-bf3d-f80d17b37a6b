resource "aws_iam_service_linked_role" "opensearch" {
  count            = var.create_service_linked_role ? 1 : 0
  aws_service_name = "opensearchservice.amazonaws.com"
}

resource "aws_cloudwatch_log_group" "opensearch_logs" {
  for_each = var.enable_logs ? toset(["INDEX_SLOW_LOGS", "SEARCH_SLOW_LOGS", "ES_APPLICATION_LOGS", "AUDIT_LOGS"]) : []

  name              = "/aws/opensearch/domains/${var.program}-${var.project}-${var.environment}-${var.name}/${each.value}"
  retention_in_days = 30

  tags = var.tags
}

resource "aws_cloudwatch_log_resource_policy" "opensearch_log_publishing_policy" {
  count = var.enable_logs ? 1 : 0

  policy_name     = join("-", [var.program, var.project, var.environment, var.name, "opensearch-log-policy"])
  policy_document = data.aws_iam_policy_document.cloudwatch_log_publishing_policy.json
}

#trivy:ignore:AVD-AWS-0042 logs are optionally enabled
resource "aws_opensearch_domain" "this" {
  domain_name    = join("-", [var.program, var.project, var.environment, var.name])
  engine_version = "OpenSearch_${var.opensearch_version}"

  cluster_config {
    instance_type            = var.instance_type
    instance_count           = var.instance_count
    dedicated_master_enabled = false
    zone_awareness_enabled   = var.instance_count > 1 ? true : false

    zone_awareness_config {
      availability_zone_count = var.instance_count > 1 ? (length(distinct([for subnet in data.aws_subnet.subnets : subnet.availability_zone])) >= 3 && var.instance_count >= 3 ? 3 : 2) : null
    }
  }

  ebs_options {
    ebs_enabled = true
    volume_size = var.ebs_volume_size
    volume_type = var.ebs_volume_type
  }

  vpc_options {
    subnet_ids         = var.subnet_ids
    security_group_ids = [aws_security_group.this.id]
  }

  snapshot_options {
    automated_snapshot_start_hour = var.automated_snapshot_start_hour
  }

  encrypt_at_rest {
    enabled    = var.encrypt_at_rest
    kms_key_id = var.encrypt_at_rest ? var.kms_key_id : null
  }

  node_to_node_encryption {
    enabled = var.node_to_node_encryption
  }

  domain_endpoint_options {
    enforce_https       = var.enforce_https
    tls_security_policy = var.tls_security_policy
  }

  advanced_options = var.advanced_options

  access_policies = data.aws_iam_policy_document.opensearch_access_policy.json

  dynamic "log_publishing_options" {
    for_each = var.enable_logs ? toset(["INDEX_SLOW_LOGS", "SEARCH_SLOW_LOGS", "ES_APPLICATION_LOGS", "AUDIT_LOGS"]) : []
    content {
      log_type                 = log_publishing_options.value
      enabled                  = true
      cloudwatch_log_group_arn = aws_cloudwatch_log_group.opensearch_logs[log_publishing_options.value].arn
    }
  }

  tags = var.tags

  depends_on = [aws_iam_service_linked_role.opensearch]
}

resource "aws_security_group" "this" {
  name        = join("-", [var.program, var.project, var.environment, var.name, "sg"])
  description = "Security group for ${var.program}-${var.project}-${var.environment}-${var.name} OpenSearch domain"
  vpc_id      = var.vpc_id

  tags = merge(
    {
      Project     = var.project
      Environment = var.environment
      Terraform   = "true"
    },
    var.tags
  )
}

resource "aws_vpc_security_group_ingress_rule" "ingress" {
  for_each          = data.aws_subnet.subnets
  description       = "HTTPS from OpenSearch subnet ${each.key} (${each.value.cidr_block})"
  from_port         = 443
  to_port           = 443
  ip_protocol       = "tcp"
  cidr_ipv4         = each.value.cidr_block
  security_group_id = aws_security_group.this.id
}

#trivy:ignore:aws-vpc-no-public-egress-sgr
resource "aws_vpc_security_group_egress_rule" "egress" {
  description       = "Allow all outbound traffic"
  from_port         = 0
  to_port           = 0
  ip_protocol       = "-1"
  cidr_ipv4         = "0.0.0.0/0"
  security_group_id = aws_security_group.this.id
}
