module "ecr_api" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name = "ecr.api"
  vpc_id       = module.vpc.this_vpc_id
  subnet_ids   = module.vpc.private_subnet_ids
}

module "ecr_dkr" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name = "ecr.dkr"
  vpc_id       = module.vpc.this_vpc_id
  subnet_ids   = module.vpc.private_subnet_ids
}

module "s3" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name  = "s3"
  endpoint_type = "Gateway"
  vpc_id        = module.vpc.this_vpc_id
  route_table_ids = concat(
    module.vpc.private_route_table_ids,
    [module.vpc.protected_route_table_id]
  )
}

module "cloudwatch_logs" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name = "logs"
  vpc_id       = module.vpc.this_vpc_id
  subnet_ids   = module.vpc.private_subnet_ids
}

module "sns" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name = "sns"
  vpc_id       = module.vpc.this_vpc_id
  subnet_ids   = module.vpc.private_subnet_ids
}

module "sqs" {
  source = "../../terraform-modules/terraform-aws-vpc-endpoint"

  program     = var.program
  project     = var.project
  environment = var.environment

  service_name = "sqs"
  vpc_id       = module.vpc.this_vpc_id
  subnet_ids   = module.vpc.private_subnet_ids
}
