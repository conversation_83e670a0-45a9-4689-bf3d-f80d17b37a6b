output "this_ecs_service_id" {
  description = "The ID of the ECS service"
  value       = try(aws_ecs_service.this.id, null)
}

output "this_ecs_service_name" {
  description = "The name of the ECS service"
  value       = try(aws_ecs_service.this.name, null)
}
output "this_ecs_service_cluster" {
  description = "The ARN of the ECS cluster where the service is deployed"
  value       = try(aws_ecs_service.this.cluster, null)
}

output "this_ecs_service_desired_count" {
  description = "The desired count of the ECS service"
  value       = try(aws_ecs_service.this.desired_count, null)
}

output "this_ecs_service_task_definition" {
  description = "The task definition used by the ECS service"
  value       = try(aws_ecs_service.this.task_definition, null)
}

output "this_ecs_service_launch_type" {
  description = "The launch type of the ECS service"
  value       = try(aws_ecs_service.this.launch_type, null)
}

output "target_group_arns" {
  description = "Map of created Target Group ARNs"
  value       = try({ for k, tg in aws_lb_target_group.this : k => tg.arn }, null)
}
