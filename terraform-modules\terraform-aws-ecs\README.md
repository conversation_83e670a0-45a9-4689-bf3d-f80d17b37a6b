# terraform-aws-ecs

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_ecs_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_cluster) | resource |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_service_linked_role.ecs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_service_linked_role) | resource |
| [aws_iam_policy_document.ecs_tasks_assume_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_container_insights"></a> [container\_insights](#input\_container\_insights) | Enable container insights for the ECS cluster | `bool` | `true` | no |
| <a name="input_create_ecs_service_linked_role"></a> [create\_ecs\_service\_linked\_role](#input\_create\_ecs\_service\_linked\_role) | Whether to create the AWSServiceRoleForECS service-linked role | `bool` | `true` | no |
| <a name="input_create_task_execution_role"></a> [create\_task\_execution\_role](#input\_create\_task\_execution\_role) | Whether to create the ECS task execution IAM role | `bool` | `true` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment stage | `string` | n/a | yes |
| <a name="input_name"></a> [name](#input\_name) | Name for the ECS cluster | `string` | `"ecs"` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_this_cluster_arn"></a> [this\_cluster\_arn](#output\_this\_cluster\_arn) | The ARN of the ECS cluster |
| <a name="output_this_cluster_id"></a> [this\_cluster\_id](#output\_this\_cluster\_id) | The ID of the ECS cluster |
| <a name="output_this_cluster_name"></a> [this\_cluster\_name](#output\_this\_cluster\_name) | The name of the ECS cluster |
| <a name="output_this_task_execution_role_arn"></a> [this\_task\_execution\_role\_arn](#output\_this\_task\_execution\_role\_arn) | The ARN of the IAM role created for ECS task execution |
| <a name="output_this_task_execution_role_name"></a> [this\_task\_execution\_role\_name](#output\_this\_task\_execution\_role\_name) | The name of the IAM role created for ECS task execution |
<!-- END_TF_DOCS -->
