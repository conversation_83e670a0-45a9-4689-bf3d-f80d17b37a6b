variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "component" {
  description = "The name of the component the file system is created for"
  type        = string
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "creation_token" {
  description = "A unique name (a maximum of 64 characters are allowed) used as reference when creating the Elastic File System to ensure idempotent file system creation. By default generated by Terraform"
  type        = string
  default     = null
}

variable "encrypted" {
  description = "If `true`, the disk will be encrypted"
  type        = bool
  default     = false
}

variable "kms_key_arn" {
  description = "The ARN for the KMS encryption key. When specifying `kms_key_arn`, encrypted needs to be set to `true`"
  type        = string
  default     = null
}

variable "mount_targets" {
  description = "List of subnet IDs to create mount targets in"
  type        = list(string)
  default     = []
}

variable "vpc_id" {
  description = "The VPC ID where the security group will be created"
  type        = string
  default     = null
}

variable "backup_policy_status" {
  description = "Backup policy status for the EFS file system. Allowed values: ENABLED, DISABLED"
  type        = string
  default     = "ENABLED"

  validation {
    condition     = contains(["ENABLED", "DISABLED"], var.backup_policy_status)
    error_message = "The backup_policy_status value must be one of: ENABLED, DISABLED."
  }
}
