variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "cidr_block" {
  description = "vpc cidr block (X.X.X.X/X notation)"
  type        = string
}

variable "public_subnets" {
  description = "List of public subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "private_subnets" {
  description = "List of private subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "protected_subnets" {
  description = "List of protected subnet details"
  type = list(object({
    cidr_block        = string
    availability_zone = string
  }))
}

variable "customer_gateway_ip_1" {
  description = "IP address of the first customer gateway (Spectrum)"
  type        = string
}

variable "customer_gateway_ip_2" {
  description = "IP address of the second customer gateway (Bright Speed)"
  type        = string
}

variable "bgp_asn" {
  description = "BGP ASN of the customer gateway"
  type        = number
}

variable "vpn_static_routes_only" {
  description = "Whether to use static routes only"
  type        = bool
}

variable "vpn_destination_cidr_blocks" {
  description = "List of destination CIDR blocks for static routes"
  type        = list(string)
}

variable "vpn_alarm_actions" {
  description = "List of ARNs to notify when alarm triggers"
  type        = list(string)
}

variable "vpn_ok_actions" {
  description = "List of ARNs to notify when alarm returns to OK state"
  type        = list(string)
}
