resource "aws_security_group" "adapter_api" {
  name        = join("-", [var.program, var.project, var.environment, "adapter-api-sg"])
  description = "Security group for Axon Adapter API service"
  vpc_id      = data.terraform_remote_state.foundation.outputs.vpc_id

  # Ingress rule will be added when ALB is created to allow traffic from ALB security group
  # For now, no ingress rules are needed as the service is not directly accessible

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  lifecycle {
    create_before_destroy = true
  }
}

# This rule will be uncommented when ALB is created
# resource "aws_security_group_rule" "adapter_api_from_alb" {
#   type                     = "ingress"
#   from_port                = 7501
#   to_port                  = 7501
#   protocol                 = "tcp"
#   source_security_group_id = aws_security_group.alb.id
#   security_group_id        = aws_security_group.adapter_api.id
#   description              = "Allow traffic from ALB"
# }

module "adapter_api" {
  source = "../skeletons/terraform-skeleton-fargate-task"

  program     = var.program
  project     = var.project
  environment = var.environment

  name = "adapter-api"

  cluster_id         = data.terraform_remote_state.foundation.outputs.cluster_id
  vpc_id             = data.terraform_remote_state.foundation.outputs.vpc_id
  subnet_ids         = data.terraform_remote_state.foundation.outputs.private_subnet_ids
  task_role_arn      = aws_iam_role.adapter_api_task_role.arn
  execution_role_arn = aws_iam_role.adapter_api_task_execution_role.arn

  cpu    = var.adapter_api_cpu
  memory = var.adapter_api_memory

  runtime_platform = {
    cpu_architecture        = "ARM64"
    operating_system_family = "LINUX"
  }

  container_definitions = jsonencode([
    {
      name  = join("-", [var.program, var.project, var.environment, "adapter-api"])
      image = var.adapter_api_image

      cpu               = var.adapter_api_cpu
      memory            = var.adapter_api_memory
      memoryReservation = var.adapter_api_memory
      essential         = true

      portMappings = [
        {
          name          = "adapter-api-7501-tcp"
          containerPort = 7501
          hostPort      = 7501
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]

      environment = [
        {
          name  = "SapApi__UseSnc"
          value = "false"
        },
        {
          name  = "SapApi__Client"
          value = "100"
        },
        {
          name  = "SapApi__Language"
          value = "EN"
        },
        {
          name  = "ASPNETCORE_ENVIRONMENT"
          value = "AWS-Development"
        },
        {
          name  = "Aws__SnsTopicPrefix"
          value = "${var.environment}-"
        },
        {
          name  = "Serilog__MinimumLevel__Override__Microsoft.AspNetCore"
          value = "Warning"
        },
        {
          name  = "Serilog__MinimumLevel__Default"
          value = "Information"
        },
        {
          name  = "Aws__Region"
          value = var.region
        },
        {
          name  = "SapApi__MaxPoolSize"
          value = "50"
        },
        {
          name  = "SapApi__IdleTimeout"
          value = "600"
        },
        {
          name  = "SapApi__TraceLevel"
          value = "0"
        },
        {
          name  = "SapApi__UseRouter"
          value = "false"
        },
        {
          name  = "SapApi__SystemNumber"
          value = "00"
        },
        {
          name  = "Serilog__MinimumLevel__Override__Microsoft"
          value = "Warning"
        },
        {
          name  = "SapApi__UseLoadBalancing"
          value = "false"
        },
        {
          name  = "MagentoApi__User"
          value = "MAGENTO_USER"
        },
        {
          name  = "AWS_REGION"
          value = var.region
        },
        {
          name  = "SapApi__BaseAddress"
          value = "http://sap-mock-service:7600"
        },
        {
          name  = "Serilog__MinimumLevel__Override__System"
          value = "Warning"
        },
        {
          name  = "SapApi__Check"
          value = "1"
        },
        {
          name  = "Aws__SqsQueuePrefix"
          value = "${var.environment}-"
        },
        {
          name  = "SapApi__SystemId"
          value = "DEV"
        },
        {
          name  = "MagentoApi__BaseAddress"
          value = "http://magento-mock-service:7500"
        },
        {
          name  = "SapApi__PoolSize"
          value = "5"
        },
        {
          name  = "SapApi__User"
          value = "RFC_USER"
        },
        {
          name  = "ASPNETCORE_URLS"
          value = "http://+:7501"
        },
        {
          name  = "SapApi__ConnectionTimeout"
          value = "30"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/aws/ecs/${var.program}-${var.environment}-${var.project}-adapter-api"
          "awslogs-region"        = var.region
          "awslogs-stream-prefix" = "ecs"
          "mode"                  = "non-blocking"
          "max-buffer-size"       = "25m"
        }
      }

      mountPoints    = []
      volumesFrom    = []
      systemControls = []
    }
  ])

  security_group_ids = [aws_security_group.adapter_api.id]
  desired_count      = var.adapter_api_desired_count

  enable_ecs_managed_tags            = true
  propagate_tags                     = "SERVICE"
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  create_cloudwatch_log_group   = true
  cloudwatch_log_retention_days = 7

  # Create ECR repository for this service
  create_ecr_repository      = true
  ecr_image_tag_mutability   = "MUTABLE"
  ecr_lifecycle_policy_count = 10
}