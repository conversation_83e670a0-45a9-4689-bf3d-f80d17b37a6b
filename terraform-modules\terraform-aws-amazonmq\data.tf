# Data sources for subnet information
data "aws_subnet" "subnets" {
  for_each = toset(var.subnet_ids)
  id       = each.value
}

data "aws_secretsmanager_random_password" "broker_password" {
  count = var.create_secrets_manager_secret && var.secrets_manager_secret_id == null ? 1 : 0

  password_length     = 20
  exclude_punctuation = true
  exclude_uppercase   = false
  exclude_lowercase   = false
  exclude_numbers     = false
  include_space       = false
}

# Get the existing secret if provided
data "aws_secretsmanager_secret_version" "existing" {
  count     = var.secrets_manager_secret_id != null ? 1 : 0
  secret_id = var.secrets_manager_secret_id
}
