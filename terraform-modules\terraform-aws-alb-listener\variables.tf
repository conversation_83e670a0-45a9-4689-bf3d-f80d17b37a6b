variable "lb_arn" {
  description = "ARN of the LB"
  type        = string
}

variable "acm_certificate_arn" {
  description = "ARN of the ACM Certificate"
  type        = string
}

variable "protocol" {
  description = "Protocol for connections from clients to the load balancer"
  type        = string
}

variable "ssl_policy" {
  default     = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  description = "Name of the SSL Policy for the listener. Required for HTTPs and TLS protocol"
  type        = string
}

variable "alpn_policy" {
  default     = "HTTP2Preferred"
  description = "Name of the Application-Layer Protocol Negotiation (ALPN) policy. Can be set if protocol is TLS"
  type        = string
}

variable "authenticate_cognito" {
  description = "Configuration block for using Amazon Cognito to authenticate users. Specify only when type is authenticate-cognito"
  type = object({
    authentication_request_extra_params = map(string)
    on_unauthenticated_request          = string
    scope                               = set(string)
    session_cookie_name                 = string
    session_timeout                     = number
    user_pool_arn                       = string
    user_pool_client_id                 = string
    user_pool_domain                    = string
    order                               = number
  })
}

variable "authenticate_oidc" {
  description = "Configuration block for an identity provider that is compliant with OpenID Connect (OIDC). Specify only when type is authenticate-oidc"
  type = object({
    authentication_request_extra_params = map(string)
    on_unauthenticated_request          = string
    scope                               = set(string)
    session_cookie_name                 = string
    session_timeout                     = string
    authorization_endpoint              = string
    client_id                           = string
    client_secret                       = string
    issuer                              = string
    token_endpoint                      = string
    user_info_endpoint                  = string
    order                               = number
  })
}

variable "fixed_response" {
  description = "Information for creating an action that returns a custom HTTP response. Required if type is fixed-response"
  type = object({
    content_type = string
    message_body = string
    status_code  = number
  })
}

variable "single_target_group_forward" {
  description = "Specify only if you want to route to a single target group. Used together with forward type"
  type = object({
    target_group_arn = string
    order            = number
  })
}

variable "forward" {
  description = "Configuration block for creating an action that distributes requests among one or more target groups. Specify only if type is forward"
  type = object({
    arn    = string
    weight = number
    order  = number

    stickiness = object({
      duration = number
      enabled  = bool
    })
  })
}

variable "redirect" {
  description = "Configuration block for creating a redirect action. Required if type is redirect"
  type = object({
    host     = string
    path     = string
    port     = number
    protocol = string
    query    = string
    status   = string
  })
}

variable "listener_rules" {
  description = "Amazon Load Balancer listener rule configuration block. You can attach several rules to one listener"
  type = object({
    priority = number
    actions = object({
      type                                = string
      order                               = number
      authentication_request_extra_params = map(string)
      on_unauthenticated_request          = string
      scope                               = set(string)
      session_cookie_name                 = string
      session_timeout                     = string
      authorization_endpoint              = string
      status_code                         = number
      client_id                           = string
      client_secret                       = string
      issuer                              = string
      token_endpoint                      = string
      user_info_endpoint                  = string
      user_pool_arn                       = string
      user_pool_client_id                 = string
      user_pool_domain                    = string
      host                                = string
      path                                = string
      port                                = number
      protocol                            = string
      query                               = string
      content_type                        = string
      message_body                        = string
      target_group_arn                    = string
      arn                                 = string
      weight                              = string

      stickiness = object({
        duration = number
        enabled  = bool
      })

      condition = object({
        host_header = list(string)

        http_header = object({
          http_header_name = string
          values           = list(string)
        })

        http_request_method = object({
          values = list(string)
        })

        path_pattern = object({
          values = list(string)
        })

        query_string = map(string)
        source_ip = object({
          values = list(string)
        })
      })
    })
  })
}

variable "routing_http_response_server_enabled" {
  description = "Enables you to allow or remove the HTTP response server header. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = bool
}

variable "routing_http_response_strict_transport_security_header_value" {
  description = "Informs browsers that the site should only be accessed using HTTPS, and that any future attempts to access it using HTTP should automatically be converted to HTTPS"
  type        = string
}

variable "routing_http_response_access_control_allow_origin_header_value" {
  description = "Specifies which origins are allowed to access the server. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_access_control_allow_methods_header_value" {
  description = "Set which HTTP methods are allowed when accessing the server from a different origin. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_access_control_allow_headers_header_value" {
  description = "Specifies which headers can be used during the request. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_access_control_allow_credentials_header_value" {
  description = "Specifies which headers the browser can expose to the requesting client. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_access_control_expose_headers_header_value" {
  description = "Specifies whether the browser should include credentials such as cookies or authentication when making requests. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_access_control_max_age_header_value" {
  description = "Specifies how long the results of a preflight request can be cached, in seconds. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_content_security_policy_header_value" {
  description = "Specifies restrictions enforced by the browser to help minimize the risk of certain types of security threats. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_x_content_type_options_header_value" {
  description = "Indicates whether the MIME types advertised in the Content-Type headers should be followed and not be changed. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_response_x_frame_options_header_value" {
  description = "Indicates whether the browser is allowed to render a page in a frame, iframe, embed or object. Can only be set if protocol is HTTP or HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_tls_version_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Tls-Version HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_tls_cipher_suite_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Tls-Cipher-Suite HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Serial-Number HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Issuer HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_subject_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Subject HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_validity_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Validity HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name" {
  description = "Enables you to modify the header name of the X-Amzn-Mtls-Clientcert-Leaf HTTP request header. Can only be set if protocol is HTTPS for Application Load Balancers"
  type        = string
}

variable "tags" {
  default     = {}
  description = "A map of tags to add to all resources"
  type        = map(string)
}
