variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Name of the ECS Service"
  type        = string
}

variable "cluster_id" {
  description = "ARN of the ECS cluster where the service will be deployed"
  type        = string
}

variable "task_definition_arn" {
  description = "ARN of the task definition that defines the containers to be deployed"
  type        = string
}

variable "desired_count" {
  description = "Number of instances of the task definition to place and keep running"
  type        = number
  default     = 1
}

variable "deployment_maximum_percent" {
  description = "Upper limit on the number of tasks that can be running during a deployment"
  type        = number
  default     = 200
}

variable "deployment_minimum_healthy_percent" {
  description = "Lower limit on the number of tasks that must remain in RUNNING state during a deployment"
  type        = number
  default     = 100
}

variable "enable_execute_command" {
  description = "Specifies whether to enable Amazon ECS Exec for the tasks within the service"
  type        = bool
  default     = false
}

variable "health_check_grace_period_seconds" {
  description = "Seconds to ignore failing load balancer health checks on newly instantiated tasks"
  type        = number
  default     = 0
}

variable "launch_type" {
  description = "Launch type on which to run your service. Valid values are EC2 and FARGATE"
  type        = string
  default     = "FARGATE"
}

variable "platform_version" {
  description = "Platform version on which to run your service. Only applicable for launch_type set to FARGATE"
  type        = string
  default     = "LATEST"
}

variable "scheduling_strategy" {
  description = "Scheduling strategy to use for the service. Valid values are REPLICA and DAEMON"
  type        = string
  default     = "REPLICA"
}

variable "force_new_deployment" {
  description = "Enable to force a new task deployment of the service"
  type        = bool
  default     = false
}

variable "wait_for_steady_state" {
  description = "Whether Terraform should wait for the service to reach a steady state"
  type        = bool
  default     = false
}

variable "propagate_tags" {
  description = "Specifies whether to propagate the tags from the task definition or the service to the tasks"
  type        = string
  default     = "SERVICE"
}

variable "enable_ecs_managed_tags" {
  description = "Specifies whether to enable Amazon ECS managed tags for the tasks within the service"
  type        = bool
  default     = true
}

variable "ordered_placement_strategy" {
  description = "Service level strategy rules that are taken into consideration during task placement"
  type = list(object({
    type  = string
    field = string
  }))
  default = []
}

variable "placement_constraints" {
  description = "Rules that are taken into consideration during task placement"
  type = list(object({
    type       = string
    expression = string
  }))
  default = []
}

variable "network_configuration" {
  description = "Network configuration for the service"
  type = object({
    subnets          = list(string)
    security_groups  = list(string)
    assign_public_ip = bool
  })
}

variable "load_balancer" {
  description = "List of load balancer rules for ECS service"
  type = list(object({
    container_name    = string
    container_port    = number
    protocol          = string
    health_check_path = string
    target_group_arn  = optional(string)
  }))

  default = []
}

variable "vpc_id" {
  description = "ID of the VPC used for load balancer target groups and service discovery"
  type        = string
}


variable "service_discovery" {
  description = "List of service discovery configurations. Supports both creation and reuse of Cloud Map services."

  type = list(object({
    container_name = string
    container_port = number

    # If creating a new service
    namespace_id   = optional(string)
    dns_ttl        = optional(number)
    routing_policy = optional(string) # Valid values: WEIGHTED or MULTIVALUE

    # If using an existing Cloud Map service
    registry_arn = optional(string)
  }))

  default = []
}

variable "capacity_provider_strategy" {
  description = "Capacity provider strategies to use for the service"
  type = list(object({
    capacity_provider = string
    weight            = number
    base              = optional(number)
  }))
  default = []
}

variable "tags" {
  description = "A map of tags to assign to resources"
  type        = map(string)
  default     = {}
}

variable "deployment_circuit_breaker_enabled" {
  description = "Whether to enable deployment circuit breaker with automatic rollback"
  type        = bool
  default     = true
}
