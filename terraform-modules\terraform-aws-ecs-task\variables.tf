variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Service name or component name"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "container_definitions" {
  description = "JSON-formatted list of container definitions"
  type        = string
}

variable "requires_compatibilities" {
  description = "A set of launch types required by the task"
  type        = list(string)
  default     = ["FARGATE"]
}

variable "network_mode" {
  description = "Docker networking mode to use for the containers"
  type        = string
  default     = "awsvpc"
}

variable "cpu" {
  description = "The number of cpu units used by the task"
  type        = string
}

variable "memory" {
  description = "The amount of memory (in MiB) used by the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role that grants permissions for ECS tasks to call AWS APIs"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role that the task can assume"
  type        = string
  default     = null
}

variable "volume_definitions" {
  description = "List of volume definitions for ECS task"
  type = list(object({
    name = string
    efs_volume_configuration = optional(object({
      file_system_id          = string
      root_directory          = optional(string)
      transit_encryption      = optional(string)
      transit_encryption_port = optional(number)
    }))
    host_path = optional(object({
      path = string
    }))
  }))
  default = []
}

variable "runtime_platform" {
  description = "Optional runtime platform configuration"
  type = object({
    operating_system_family = string
    cpu_architecture        = optional(string)
  })
  default = null
}
