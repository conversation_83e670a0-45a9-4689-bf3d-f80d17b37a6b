################################################################################
# Cluster
################################################################################

output "this_rds_cluster_arn" {
  description = "Amazon Resource Name (ARN) of cluster"
  value       = try(aws_rds_cluster.this.arn, null)
}

output "this_rds_cluster_id" {
  description = "The RDS Cluster Identifier"
  value       = try(aws_rds_cluster.this.id, null)
}

output "this_rds_cluster_endpoint" {
  description = "Writer endpoint for the cluster"
  value       = try(aws_rds_cluster.this.endpoint, null)
}

output "this_rds_cluster_reader_endpoint" {
  description = "A read-only endpoint for the cluster, automatically load-balanced across replicas"
  value       = try(aws_rds_cluster.this.reader_endpoint, null)
}

output "this_rds_cluster_engine_version_actual" {
  description = "The running version of the cluster database"
  value       = try(aws_rds_cluster.this.engine_version_actual, null)
}

output "this_rds_cluster_port" {
  description = "The database port"
  value       = try(aws_rds_cluster.this.port, null)
}

output "this_rds_cluster_master_username" {
  description = "The database master username"
  value       = try(aws_rds_cluster.this.master_username, null)
  sensitive   = true
}

output "this_rds_cluster_master_password" {
  description = "The database master password"
  value       = try(aws_rds_cluster.this.master_password, null)
  sensitive   = true
}

output "this_rds_cluster_master_user_secret" {
  description = "The generated database master user secret"
  value       = try(aws_rds_cluster.this.master_user_secret, null)
}

output "this_rds_cluster_hosted_zone_id" {
  description = "The Route53 Hosted Zone ID of the endpoint"
  value       = try(aws_rds_cluster.this.hosted_zone_id, null)
}

output "this_rds_cluster_ca_certificate_identifier" {
  description = "CA identifier of the CA certificate used for the DB instance's server certificate"
  value       = try(aws_rds_cluster.this.ca_certificate_identifier, null)
}

output "this_rds_cluster_ca_certificate_valid_till" {
  description = "Expiration date of the DB instance’s server certificate"
  value       = try(aws_rds_cluster.this.ca_certificate_valid_till, null)
}

################################################################################
# Cluster Instance(s)
################################################################################

# Writer
output "this_rds_cluster_instance_writer_id" {
  description = "A cluster writer instance id"
  value       = aws_rds_cluster_instance.writer.id
}

output "this_rds_cluster_instance_writer_instance_class" {
  description = "A cluster writer instance class"
  value       = aws_rds_cluster_instance.writer.instance_class
}

output "this_rds_cluster_instance_writer_endpoint" {
  description = "A cluster writer instance endpoint"
  value       = aws_rds_cluster_instance.writer.endpoint
}

output "this_rds_cluster_instance_writer_arn" {
  description = "A cluster writer instance arn"
  value       = aws_rds_cluster_instance.writer.arn
}

# Reader's
output "rds_cluster_instance_reader_ids" {
  description = "Map of reader instance IDs"
  value = (
    length(aws_rds_cluster_instance.reader) > 0 ?
    { for k, inst in aws_rds_cluster_instance.reader : k => inst.id } :
    null
  )
}

output "rds_cluster_instance_reader_instance_classes" {
  description = "Map of reader instance classes"
  value = (
    length(aws_rds_cluster_instance.reader) > 0 ?
    { for k, inst in aws_rds_cluster_instance.reader : k => inst.instance_class } :
    null
  )
}

output "rds_cluster_instance_reader_endpoints" {
  description = "Map of reader instance endpoints"
  value = (
    length(aws_rds_cluster_instance.reader) > 0 ?
    { for k, inst in aws_rds_cluster_instance.reader : k => inst.endpoint } :
    null
  )
}

output "rds_cluster_instance_reader_arns" {
  description = "Map of reader instance ARNs"
  value = (
    length(aws_rds_cluster_instance.reader) > 0 ?
    { for k, inst in aws_rds_cluster_instance.reader : k => inst.arn } :
    null
  )
}

################################################################################
# Enhanced Monitoring
################################################################################

output "this_iam_role_rds_enhanced_monitoring_name" {
  description = "Name of the enhanced monitoring role"
  value       = try(aws_iam_role.rds_enhanced_monitoring.name, null)
}

output "this_iam_role_rds_enhanced_monitoring_arn" {
  description = "Amazon Resource Name (ARN) of enhanced monitoring role"
  value       = try(aws_iam_role.rds_enhanced_monitoring.arn, null)
}

output "this_iam_role_rds_enhanced_monitoring_unique_id" {
  description = "Unique string identifying the enhanced monitoring role"
  value       = try(aws_iam_role.rds_enhanced_monitoring.unique_id, null)
}

################################################################################
# Security Group
################################################################################

output "security_group_id" {
  description = "Security group ID of the cluster"
  value       = try(aws_security_group.this[*].id, null)
}

################################################################################
# Managed Secret Rotation
################################################################################

output "this_secretsmanager_secret_rotation_rotation_enabled" {
  description = "Specifies whether automatic rotation is enabled"
  value       = try(aws_secretsmanager_secret_rotation.this[*].rotation_enabled, null)
}
