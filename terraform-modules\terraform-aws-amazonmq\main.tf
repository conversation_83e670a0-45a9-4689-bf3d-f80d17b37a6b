resource "aws_security_group" "this" {
  name        = join("-", [var.program, var.project, var.environment, var.name, "broker", lower(var.engine_type), "sg"])
  description = "Security group for ${var.program}-${var.project}-${var.environment}-${var.name}-broker-${lower(var.engine_type)}"
  vpc_id      = var.vpc_id

  tags = var.tags
}

resource "aws_vpc_security_group_ingress_rule" "broker_subnet_access" {
  for_each          = toset(var.subnet_ids)
  security_group_id = aws_security_group.this.id
  description       = "Allow traffic from subnet ${each.value} to ${var.program}-${var.project}-${var.environment}-${var.name}-broker-${lower(var.engine_type)}"
  cidr_ipv4         = data.aws_subnet.subnets[each.value].cidr_block
  ip_protocol       = "tcp"
  from_port         = 5671
  to_port           = 5671
}

resource "aws_vpc_security_group_ingress_rule" "broker_management_console" {
  for_each          = toset(var.subnet_ids)
  security_group_id = aws_security_group.this.id
  description       = "Allow access to ${var.program}-${var.project}-${var.environment}-${var.name}-broker-${lower(var.engine_type)} management console from subnet ${each.value}"
  cidr_ipv4         = data.aws_subnet.subnets[each.value].cidr_block
  ip_protocol       = "tcp"
  from_port         = 15671
  to_port           = 15671
}

#trivy:ignore:aws-vpc-no-public-egress-sgr
resource "aws_vpc_security_group_egress_rule" "broker_allow_all" {
  security_group_id = aws_security_group.this.id
  description       = "Allow all outbound traffic"
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

resource "aws_secretsmanager_secret" "this" {
  count       = var.create_secrets_manager_secret && var.secrets_manager_secret_id == null ? 1 : 0
  name        = join("-", [var.program, var.project, var.environment, var.name, "broker", "credentials"])
  description = "Broker credentials for ${var.program}-${var.project}-${var.environment}-${var.name}-broker-${lower(var.engine_type)}"

  kms_key_id = var.secrets_manager_kms_key_id

  tags = var.tags
}

resource "aws_secretsmanager_secret_version" "this" {
  count     = var.create_secrets_manager_secret && var.secrets_manager_secret_id == null ? 1 : 0
  secret_id = aws_secretsmanager_secret.this[0].id
  secret_string = jsonencode({
    username = var.username
    password = data.aws_secretsmanager_random_password.broker_password[0].random_password
  })
}

#trivy:ignore:avd-aws-0070 logging not available for rabbitmq engine
#trivy:ignore:avd-aws-0071 logging not available for rabbitmq engine
resource "aws_mq_broker" "this" {
  broker_name = join("-", [var.program, var.project, var.environment, var.name, "broker", lower(var.engine_type)])

  engine_type        = var.engine_type
  engine_version     = var.engine_version
  host_instance_type = var.broker_instance_type
  deployment_mode    = var.deployment_mode

  security_groups = [aws_security_group.this.id]
  subnet_ids      = var.subnet_ids

  publicly_accessible = var.publicly_accessible

  user {
    username = (
      var.secrets_manager_secret_id != null
      ? jsondecode(data.aws_secretsmanager_secret_version.existing[0].secret_string)["username"]
      : jsondecode(aws_secretsmanager_secret_version.this[0].secret_string)["username"]
    )
    password = (
      var.secrets_manager_secret_id != null
      ? jsondecode(data.aws_secretsmanager_secret_version.existing[0].secret_string)["password"]
      : jsondecode(aws_secretsmanager_secret_version.this[0].secret_string)["password"]
    )
  }

  auto_minor_version_upgrade = var.auto_minor_version_upgrade
  apply_immediately          = var.apply_immediately

  maintenance_window_start_time {
    day_of_week = var.maintenance_window_start_time.day_of_week
    time_of_day = var.maintenance_window_start_time.time_of_day
    time_zone   = var.maintenance_window_start_time.time_zone
  }

  encryption_options {
    use_aws_owned_key = var.kms_key_id == null
    kms_key_id        = var.kms_key_id
  }

  configuration {
    id       = aws_mq_configuration.this.id
    revision = aws_mq_configuration.this.latest_revision
  }

  tags = var.tags
}

resource "aws_mq_configuration" "this" {
  name           = join("-", [var.program, var.project, var.environment, var.name, "broker", lower(var.engine_type), "config"])
  engine_type    = var.engine_type
  engine_version = var.engine_version

  data = var.broker_configuration

  tags = var.tags
}
