################################################################################
# DB Subnet Group
################################################################################

resource "aws_db_subnet_group" "this" {

  name        = join("-", [var.program, var.project, var.environment, "aurora", "subnet-group", var.component])
  description = "RDS subnet group"
  subnet_ids  = var.db_subnet_ids

  tags = var.tags
}

################################################################################
# Cluster
################################################################################

resource "aws_rds_cluster" "this" {

  apply_immediately                     = var.apply_immediately
  backup_retention_period               = var.backup_retention_period
  cluster_identifier                    = join("-", [var.program, var.project, var.environment, "aurora", "cluster", var.component])
  database_name                         = var.component
  delete_automated_backups              = var.delete_automated_backups
  deletion_protection                   = var.deletion_protection
  ca_certificate_identifier             = var.ca_certificate_identifier
  engine                                = var.engine
  storage_encrypted                     = var.storage_encrypted
  engine_version                        = var.engine_version
  db_subnet_group_name                  = aws_db_subnet_group.this.name
  kms_key_id                            = var.kms_key_id
  manage_master_user_password           = var.manage_master_user_password ? var.manage_master_user_password : null
  master_user_secret_kms_key_id         = var.manage_master_user_password ? var.master_user_secret_kms_key_id : null
  master_password                       = !var.manage_master_user_password ? var.master_password : null
  master_username                       = var.master_username != null ? var.master_username : var.project
  monitoring_interval                   = var.cluster_monitoring_interval
  monitoring_role_arn                   = aws_iam_role.rds_enhanced_monitoring.arn
  performance_insights_enabled          = var.cluster_performance_insights_enabled
  performance_insights_kms_key_id       = var.cluster_performance_insights_kms_key_id
  performance_insights_retention_period = var.cluster_performance_insights_retention_period
  port                                  = var.db_port
  preferred_backup_window               = var.preferred_backup_window
  preferred_maintenance_window          = var.preferred_maintenance_window

  skip_final_snapshot    = var.skip_final_snapshot
  tags                   = var.tags
  vpc_security_group_ids = aws_security_group.this.id
}

################################################################################
# Cluster Instance(s)
################################################################################

resource "aws_rds_cluster_instance" "writer" {

  identifier                 = join("-", [var.program, var.project, var.environment, "aurora", "writer", var.component])
  cluster_identifier         = aws_rds_cluster.this.id
  instance_class             = var.writer_instance_class
  engine                     = var.engine
  engine_version             = var.engine_version
  apply_immediately          = var.apply_immediately
  auto_minor_version_upgrade = var.auto_minor_version_upgrade

  monitoring_interval = var.cluster_monitoring_interval
  monitoring_role_arn = aws_iam_role.rds_enhanced_monitoring.arn

  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_kms_key_id       = var.performance_insights_kms_key_id
  performance_insights_retention_period = var.performance_insights_retention_period

  publicly_accessible = var.publicly_accessible
  tags                = merge(var.tags, { Role = "writer" })
}

resource "aws_rds_cluster_instance" "reader" {
  for_each = var.reader_instances

  identifier                 = join("-", [var.program, var.project, var.environment, "aurora", "reader", var.component, each.key])
  cluster_identifier         = aws_rds_cluster.this.id
  instance_class             = each.value.instance_class
  engine                     = var.engine
  engine_version             = var.engine_version
  apply_immediately          = var.apply_immediately
  auto_minor_version_upgrade = var.auto_minor_version_upgrade

  monitoring_interval = var.cluster_monitoring_interval
  monitoring_role_arn = aws_iam_role.rds_enhanced_monitoring.arn

  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_kms_key_id       = var.performance_insights_kms_key_id
  performance_insights_retention_period = var.performance_insights_retention_period

  publicly_accessible = var.publicly_accessible
  tags                = merge(var.tags, { Role = "reader" })
}

################################################################################
# Enhanced Monitoring
################################################################################

resource "aws_iam_role" "rds_enhanced_monitoring" {

  name                = join("-", [var.program, var.project, var.environment, "rds", var.component, "enhanced-monitoring-role"])
  assume_role_policy  = data.aws_iam_policy_document.monitoring_rds_assume_role.json
  managed_policy_arns = data.aws_iam_policy.aws-rds-enhanced-monitoring.arn

  tags = var.tags
}

################################################################################
# Security Group
################################################################################

resource "aws_security_group" "this" {

  name        = join("-", [var.program, var.project, var.environment, "rds-sg", var.component])
  vpc_id      = var.vpc_id
  description = join(" ", ["Control traffic to/from RDS Aurora", var.component])

  tags = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group_rule" "this" {
  for_each = var.security_group_rules

  type                     = each.value.type
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  security_group_id        = aws_security_group.this.id
  cidr_blocks              = try(each.value.cidr_blocks, null)
  description              = try(each.value.description, null)
  source_security_group_id = try(each.value.source_security_group_id, null)
}

################################################################################
# Managed Secret Rotation
################################################################################

resource "aws_secretsmanager_secret_rotation" "this" {
  count = var.manage_master_user_password ? 1 : 0

  secret_id          = aws_rds_cluster.this.master_user_secret[0].secret_arn
  rotate_immediately = var.master_user_password_rotate_immediately

  rotation_rules {
    automatically_after_days = var.master_user_password_rotation_automatically_after_days
    duration                 = var.master_user_password_rotation_duration
    schedule_expression      = var.master_user_password_rotation_schedule_expression
  }
}
