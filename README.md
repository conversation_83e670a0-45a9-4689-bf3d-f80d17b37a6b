# evolution-iac

## Development in a Dev Container (Cursor/VS Code)

This repository supports development in a Dev Container for a consistent and portable Terraform environment.

### Getting Started

1. **Install [Docker](https://www.docker.com/get-started/)** on your machine.
2. **Open this project in [Cursor](https://www.cursor.so/) or [VS Code](https://code.visualstudio.com/)**.
3. If prompted, select **"Reopen in Container"**. Or, open the Command Palette and run:
   - `Dev Containers: Reopen in Container`

### What's Included
- **Terraform CLI** (v1.9.0)
- **AWS CLI**
- Recommended VS Code extensions for Terraform and Docker
- Automatic mounting of your local `~/.aws` credentials for AWS access

### Why Use a Dev Container?
- Consistent environment for all contributors
- No need to install Terraform or AWS CLI on your host
- Easy onboarding and reproducible builds

### Customization
You can modify the Dev Container by editing files in the `.devcontainer/` directory:
- `Dockerfile` for tools and dependencies
- `devcontainer.json` for settings, extensions, and mounts
