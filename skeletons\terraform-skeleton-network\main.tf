module "vpc" {
  source = "../../terraform-modules/terraform-aws-vpc"

  program     = var.program
  project     = var.project
  environment = var.environment

  cidr_block        = var.cidr_block
  public_subnets    = var.public_subnets
  private_subnets   = var.private_subnets
  protected_subnets = var.protected_subnets
}

resource "aws_route" "vpn_remote_primary" {
  for_each = toset([
    "**********/16"   # DEV Remote only
  ])

  route_table_id         = module.vpc.protected_route_table_id
  destination_cidr_block = each.value
  gateway_id             = module.vpn_primary.this_vpn_gateway_id
}

resource "aws_route" "vpn_remote_secondary" {
  for_each = toset([
    "**********/16"   # DEV Remote only
  ])

  route_table_id         = module.vpc.protected_route_table_id
  destination_cidr_block = each.value
  gateway_id             = module.vpn_secondary.this_vpn_gateway_id
}

module "vpn_primary" {
  source = "../../terraform-modules/terraform-aws-vpn"

  program     = "${var.program}-primary"
  project     = var.project
  environment = var.environment

  vpc_id                  = module.vpc.this_vpc_id
  customer_gateway_ip     = var.customer_gateway_ip_1  # Spectrum
  bgp_asn                 = var.bgp_asn
  static_routes_only      = var.vpn_static_routes_only
  destination_cidr_blocks = var.vpn_destination_cidr_blocks
  route_table_id          = module.vpc.protected_route_table_id

  # Tunnel 1 configuration - AWS limits: Phase1 max 28800s, Phase2 max 3600s
  tunnel1_phase1_encryption_algorithms = ["AES256"]
  tunnel1_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase1_dh_group_numbers      = [19]
  tunnel1_phase1_lifetime_seconds      = 28800  # Max allowed by AWS (was 86400)
  tunnel1_phase2_encryption_algorithms = ["AES256"]
  tunnel1_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase2_dh_group_numbers      = [19]
  tunnel1_phase2_lifetime_seconds      = 3600   # Max allowed by AWS (was 28800)

  # Tunnel 2 configuration - AWS limits: Phase1 max 28800s, Phase2 max 3600s
  tunnel2_phase1_encryption_algorithms = ["AES256"]
  tunnel2_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase1_dh_group_numbers      = [19]
  tunnel2_phase1_lifetime_seconds      = 28800  # Max allowed by AWS (was 86400)
  tunnel2_phase2_encryption_algorithms = ["AES256"]
  tunnel2_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase2_dh_group_numbers      = [19]
  tunnel2_phase2_lifetime_seconds      = 3600   # Max allowed by AWS (was 28800)

  alarm_actions = var.vpn_alarm_actions
  ok_actions    = var.vpn_ok_actions
}

module "vpn_secondary" {
  source = "../../terraform-modules/terraform-aws-vpn"

  program     = "${var.program}-secondary"
  project     = var.project
  environment = var.environment

  vpc_id                  = module.vpc.this_vpc_id
  customer_gateway_ip     = var.customer_gateway_ip_2  # Bright Speed
  bgp_asn                 = var.bgp_asn
  static_routes_only      = var.vpn_static_routes_only
  destination_cidr_blocks = var.vpn_destination_cidr_blocks
  route_table_id          = module.vpc.protected_route_table_id

  # Tunnel 1 configuration - AWS limits: Phase1 max 28800s, Phase2 max 3600s
  tunnel1_phase1_encryption_algorithms = ["AES256"]
  tunnel1_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase1_dh_group_numbers      = [19]
  tunnel1_phase1_lifetime_seconds      = 28800  # Max allowed by AWS (was 86400)
  tunnel1_phase2_encryption_algorithms = ["AES256"]
  tunnel1_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase2_dh_group_numbers      = [19]
  tunnel1_phase2_lifetime_seconds      = 3600   # Max allowed by AWS (was 28800)

  # Tunnel 2 configuration - AWS limits: Phase1 max 28800s, Phase2 max 3600s
  tunnel2_phase1_encryption_algorithms = ["AES256"]
  tunnel2_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase1_dh_group_numbers      = [19]
  tunnel2_phase1_lifetime_seconds      = 28800  # Max allowed by AWS (was 86400)
  tunnel2_phase2_encryption_algorithms = ["AES256"]
  tunnel2_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase2_dh_group_numbers      = [19]
  tunnel2_phase2_lifetime_seconds      = 3600   # Max allowed by AWS (was 28800)

  alarm_actions = var.vpn_alarm_actions
  ok_actions    = var.vpn_ok_actions
}
