module "vpc" {
  source = "../../terraform-modules/terraform-aws-vpc"

  program     = var.program
  project     = var.project
  environment = var.environment

  cidr_block        = var.cidr_block
  public_subnets    = var.public_subnets
  private_subnets   = var.private_subnets
  protected_subnets = var.protected_subnets
}

resource "aws_route" "vpn_remote" {
  for_each = toset([
    "**********/16",  # DEV Remote
    "**********/16",  # PROD Remote
    "**********/16"   # SIT Remote
  ])

  route_table_id         = module.vpc.this_route_table_protected_id
  destination_cidr_block = each.value
  gateway_id             = module.vpn.this_vpn_gateway_id
}

module "vpn" {
  source = "../../terraform-modules/terraform-aws-vpn"
  
  program     = var.program
  project     = var.project
  environment = var.environment

  vpc_id                  = module.vpc.this_vpc_id
  customer_gateway_ip     = var.customer_gateway_ip
  bgp_asn                 = var.bgp_asn
  static_routes_only      = var.vpn_static_routes_only
  destination_cidr_blocks = var.vpn_destination_cidr_blocks
  route_table_id          = module.vpc.this_route_table_protected_id

  # Tunnel 1 configuration
  tunnel1_phase1_encryption_algorithms = ["AES256"]
  tunnel1_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase1_dh_group_numbers      = [19]
  tunnel1_phase1_lifetime_seconds      = 86400
  tunnel1_phase2_encryption_algorithms = ["AES256"]
  tunnel1_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel1_phase2_dh_group_numbers      = [19]
  tunnel1_phase2_lifetime_seconds      = 28800
  
  # Tunnel 2 configuration
  tunnel2_phase1_encryption_algorithms = ["AES256"]
  tunnel2_phase1_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase1_dh_group_numbers      = [19]
  tunnel2_phase1_lifetime_seconds      = 86400
  tunnel2_phase2_encryption_algorithms = ["AES256"]
  tunnel2_phase2_integrity_algorithms  = ["SHA2-256"]
  tunnel2_phase2_dh_group_numbers      = [19]
  tunnel2_phase2_lifetime_seconds      = 28800

  alarm_actions = var.vpn_alarm_actions
  ok_actions    = var.vpn_ok_actions
}
