output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.ecs.this_cluster_id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = module.ecs.this_cluster_arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = module.ecs.this_cluster_name
}

output "task_execution_role_arn" {
  description = "The ARN of the IAM role created for ECS task execution"
  value       = module.ecs.this_task_execution_role_arn
}

output "task_execution_role_name" {
  description = "The name of the IAM role created for ECS task execution"
  value       = module.ecs.this_task_execution_role_name
}