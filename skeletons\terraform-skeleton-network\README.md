# terraform-module tempalte

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

No providers.

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ecr_api"></a> [ecr\_api](#module\_ecr\_api) | ../../terraform-modules/terraform-aws-vpc-endpoint | n/a |
| <a name="module_ecr_dkr"></a> [ecr\_dkr](#module\_ecr\_dkr) | ../../terraform-modules/terraform-aws-vpc-endpoint | n/a |
| <a name="module_vpc"></a> [vpc](#module\_vpc) | ../../terraform-modules/terraform-aws-vpc | n/a |

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cidr_block"></a> [cidr\_block](#input\_cidr\_block) | vpc cidr block (X.X.X.X/X notation) | `string` | n/a | yes |
| <a name="input_environment"></a> [environment](#input\_environment) | environment stage | `string` | n/a | yes |
| <a name="input_private_subnets"></a> [private\_subnets](#input\_private\_subnets) | List of private subnet details | <pre>list(object({<br/>    cidr_block        = string<br/>    availability_zone = string<br/>  }))</pre> | n/a | yes |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | project name | `string` | n/a | yes |
| <a name="input_protected_subnets"></a> [protected\_subnets](#input\_protected\_subnets) | List of protected subnet details | <pre>list(object({<br/>    cidr_block        = string<br/>    availability_zone = string<br/>  }))</pre> | n/a | yes |
| <a name="input_public_subnets"></a> [public\_subnets](#input\_public\_subnets) | List of public subnet details | <pre>list(object({<br/>    cidr_block        = string<br/>    availability_zone = string<br/>  }))</pre> | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_ecr_api"></a> [ecr\_api](#output\_ecr\_api) | ECR.API VPC Endpoint outputs |
| <a name="output_ecr_dkr"></a> [ecr\_dkr](#output\_ecr\_dkr) | ECR.DKR VPC Endpoint outputs |
| <a name="output_vpc"></a> [vpc](#output\_vpc) | VPC module outputs |
<!-- END_TF_DOCS -->
