variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Name of the OpenSearch domain"
  type        = string
  default     = "magento-opensearch"
}

variable "opensearch_version" {
  description = "Version of OpenSearch to deploy"
  type        = string
  default     = "2.5" # Compatible with <PERSON>gento
}

variable "instance_type" {
  description = "Instance type for OpenSearch data nodes"
  type        = string
  default     = "t3.small.search" # For dev/test environments
}

variable "instance_count" {
  description = "Number of instances in the cluster"
  type        = number
  default     = 1
}

variable "ebs_volume_size" {
  description = "Size of EBS volumes attached to data nodes (in GB)"
  type        = number
  default     = 10
}

variable "ebs_volume_type" {
  description = "Type of EBS volumes attached to data nodes"
  type        = string
  default     = "gp3"
}

variable "vpc_id" {
  description = "ID of the VPC where to create the OpenSearch domain"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the OpenSearch domain"
  type        = list(string)
}


variable "automated_snapshot_start_hour" {
  description = "Hour during which the service takes an automated daily snapshot of the indices"
  type        = number
  default     = 0
}

variable "encrypt_at_rest" {
  description = "Whether to enable encryption at rest"
  type        = bool
  default     = true
}

variable "node_to_node_encryption" {
  description = "Whether to enable node-to-node encryption"
  type        = bool
  default     = true
}

variable "enforce_https" {
  description = "Whether to require HTTPS for all traffic to the domain"
  type        = bool
  default     = true
}

variable "tls_security_policy" {
  description = "Security policy for TLS"
  type        = string
  default     = "Policy-Min-TLS-1-2-2019-07"
}

variable "advanced_options" {
  description = "Key-value string pairs to specify advanced configuration options"
  type        = map(string)
  default = {
    "rest.action.multi.allow_explicit_index" = "true"
  }
}

variable "create_service_linked_role" {
  description = "Whether to create the service linked role for OpenSearch service"
  type        = bool
  default     = true
}

variable "access_policy_conditions" {
  description = "List of condition blocks to apply to the access policy. Each condition block should have 'test', 'variable', and 'values' keys. Set to empty list for no conditions."
  type = list(object({
    test     = string
    variable = string
    values   = list(string)
  }))
  default = []
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "kms_key_id" {
  description = "KMS key ID to encrypt the OpenSearch domain with. If not specified, the default AWS KMS key for OpenSearch will be used."
  type        = string
  default     = null
}

variable "enable_logs" {
  description = "Whether to enable CloudWatch logging for the OpenSearch domain. If true, creates CloudWatch log groups and configures all OpenSearch logs to be sent to CloudWatch."
  type        = bool
  default     = false
}
