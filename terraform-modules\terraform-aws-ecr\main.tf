resource "aws_ecr_repository" "this" {
  name                 = join("-", [var.program, var.project, var.environment, var.repository_name])
  image_tag_mutability = var.image_tag_mutability

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = var.kms_key_id
  }

  image_scanning_configuration {
    scan_on_push = var.enable_image_scanning
  }
}

data "aws_iam_policy_document" "this" {
  statement {
    sid    = "AllowReadOnlyPull"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = var.read_only_arns
    }

    actions = [
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability"
    ]
  }

  statement {
    sid    = "AllowPush"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = var.write_arns
    }

    actions = [
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "ecr:PutImage",
      "ecr:InitiateLayerUpload",
      "ecr:UploadLayerPart",
      "ecr:CompleteLayerUpload"
    ]
  }
}

resource "aws_ecr_repository_policy" "this" {
  repository = aws_ecr_repository.this.name
  policy     = data.aws_iam_policy_document.this.json

}

data "aws_ecr_lifecycle_policy_document" "this" {
  rule {
    priority    = 1
    description = "Keep only last ${var.image_count} images with any tag"
    selection {
      tag_status      = "tagged"
      tag_prefix_list = var.prefix_tag
      count_type      = "imageCountMoreThan"
      count_number    = var.image_count
    }
    action {
      type = "expire"
    }
  }

  rule {
    priority    = 2
    description = "Expire untagged images after ${var.expire_images} days"
    selection {
      tag_status   = "untagged"
      count_type   = "sinceImagePushed"
      count_unit   = "days"
      count_number = var.expire_images
    }
    action {
      type = "expire"
    }
  }
}

resource "aws_ecr_lifecycle_policy" "this" {
  repository = aws_ecr_repository.this.name
  policy     = data.aws_ecr_lifecycle_policy_document.this.json

}
