# Bootstrap configuration to create state storage infrastructure
# Run this first with local state, then migrate other projects to remote state
# This creates SHARED infrastructure for all dev environment projects

terraform {
  required_version = ">= 1.9.0"
  required_providers {
    aws = {
      source  = "registry.opentofu.org/hashicorp/aws"
      version = "~> 5.94"
    }
  }
  # This uses LOCAL state to bootstrap the remote state infrastructure
}

provider "aws" {
  region  = "us-east-2"
  profile = "terraform"
}

# KMS key for state encryption (shared across all dev projects)
resource "aws_kms_key" "terraform_state" {
  description             = "KMS key for Evolution Terraform state encryption - Dev Environment"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = {
    Name        = "evolution-terraform-state-key-dev"
    Environment = "dev"
    Purpose     = "evolution-terraform-state-encryption"
    Team        = "enterprise-digitalization"
  }
}

resource "aws_kms_alias" "terraform_state" {
  name          = "alias/evolution-terraform-state-key-dev"
  target_key_id = aws_kms_key.terraform_state.key_id
}

# S3 bucket for ALL dev environment state files
resource "aws_s3_bucket" "terraform_state" {
  bucket = "evolution-terraform-state-dev"

  tags = {
    Name        = "Evolution Terraform State Bucket - Dev Environment"
    Environment = "dev"
    Purpose     = "evolution-terraform-state"
    Scope       = "all-dev-projects"
    Team        = "enterprise-digitalization"
  }
}

# Enable versioning
resource "aws_s3_bucket_versioning" "terraform_state" {
  bucket = aws_s3_bucket.terraform_state.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Enable server-side encryption with KMS
resource "aws_s3_bucket_server_side_encryption_configuration" "terraform_state" {
  bucket = aws_s3_bucket.terraform_state.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.terraform_state.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# Block public access
resource "aws_s3_bucket_public_access_block" "terraform_state" {
  bucket = aws_s3_bucket.terraform_state.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable lifecycle management
resource "aws_s3_bucket_lifecycle_configuration" "terraform_state" {
  bucket = aws_s3_bucket.terraform_state.id

  rule {
    id     = "state_lifecycle"
    status = "Enabled"

    filter {
      prefix = "" # Apply to all objects
    }

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
  }
}

# DynamoDB table for state locking (environment-specific)
resource "aws_dynamodb_table" "terraform_state_lock" {
  name         = "evolution-terraform-state-lock-dev"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  tags = {
    Name        = "Evolution Terraform State Lock Table - Dev Environment"
    Environment = "dev"
    Purpose     = "evolution-terraform-state-locking"
    Scope       = "dev-environment-projects"
    Team        = "enterprise-digitalization"
  }
}

# Outputs for reference
output "s3_bucket_name" {
  description = "Name of the S3 bucket for state storage"
  value       = aws_s3_bucket.terraform_state.id
}

output "dynamodb_table_name" {
  description = "Name of the DynamoDB table for state locking (shared)"
  value       = aws_dynamodb_table.terraform_state_lock.name
}

output "kms_key_id" {
  description = "ID of the KMS key for state encryption"
  value       = aws_kms_key.terraform_state.key_id
}

output "kms_key_arn" {
  description = "ARN of the KMS key for state encryption"
  value       = aws_kms_key.terraform_state.arn
}

output "kms_alias" {
  description = "KMS key alias for state encryption"
  value       = aws_kms_alias.terraform_state.name
} 