# terraform-module tempalte

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_ecr_lifecycle_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_lifecycle_policy) | resource |
| [aws_ecr_repository.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository_policy) | resource |
| [aws_ecr_lifecycle_policy_document.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ecr_lifecycle_policy_document) | data source |
| [aws_iam_policy_document.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_enable_image_scanning"></a> [enable\_image\_scanning](#input\_enable\_image\_scanning) | Enable image scanning | `bool` | `true` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | environment stage | `string` | n/a | yes |
| <a name="input_expire_images"></a> [expire\_images](#input\_expire\_images) | expire images - resource (aws\_ecr\_lifecycle\_policy) | `number` | `30` | no |
| <a name="input_image_count"></a> [image\_count](#input\_image\_count) | image count - resource (aws\_ecr\_lifecycle\_policy) | `number` | `10` | no |
| <a name="input_image_tag_mutability"></a> [image\_tag\_mutability](#input\_image\_tag\_mutability) | image tag mutability | `string` | `"IMMUTABLE"` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS ECS Key ID | `string` | n/a | yes |
| <a name="input_prefix_tag"></a> [prefix\_tag](#input\_prefix\_tag) | list prefix tag - resource (aws\_ecr\_lifecycle\_policy) | `list(string)` | <pre>[<br/>  "MAGENTO_",<br/>  "PWA_"<br/>]</pre> | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | project name | `string` | n/a | yes |
| <a name="input_read_only_arns"></a> [read\_only\_arns](#input\_read\_only\_arns) | List of ARNs that can pull images - resource (aws\_ecr\_repository\_policy) | `list(string)` | n/a | yes |
| <a name="input_repository_name"></a> [repository\_name](#input\_repository\_name) | ECR name | `string` | n/a | yes |
| <a name="input_write_arns"></a> [write\_arns](#input\_write\_arns) | List of ARNs that can push images - resource (aws\_ecr\_repository\_policy) | `list(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_ecr_registry_arn"></a> [ecr\_registry\_arn](#output\_ecr\_registry\_arn) | arn registry ECR |
| <a name="output_ecr_registry_id"></a> [ecr\_registry\_id](#output\_ecr\_registry\_id) | ID registry ECR |
| <a name="output_ecr_repository_name"></a> [ecr\_repository\_name](#output\_ecr\_repository\_name) | Nane repository ECR |
| <a name="output_ecr_repository_url"></a> [ecr\_repository\_url](#output\_ecr\_repository\_url) | URL repository ECR |
<!-- END_TF_DOCS -->
