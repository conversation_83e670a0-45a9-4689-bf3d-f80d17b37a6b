variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Name for the AmazonMQ resources"
  type        = string
  default     = "magento"
}

variable "vpc_id" {
  description = "VPC ID where AmazonMQ will be deployed"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs where broker will be deployed"
  type        = list(string)
}

variable "engine_type" {
  description = "Type of broker engine"
  type        = string
  default     = "RabbitMQ"
}

variable "engine_version" {
  description = "Version of the broker engine"
  type        = string
  default     = "3.13"
}

variable "broker_instance_type" {
  description = "Instance type of the broker"
  type        = string
  default     = "mq.t3.micro"
}

variable "deployment_mode" {
  description = "Deployment mode of the broker"
  type        = string
  default     = "SINGLE_INSTANCE"
  validation {
    condition     = contains(["SINGLE_INSTANCE", "ACTIVE_STANDBY_MULTI_AZ", "CLUSTER_MULTI_AZ"], var.deployment_mode)
    error_message = "Valid values are SINGLE_INSTANCE, ACTIVE_STANDBY_MULTI_AZ, or CLUSTER_MULTI_AZ."
  }
}

variable "publicly_accessible" {
  description = "Whether the broker should be publicly accessible"
  type        = bool
  default     = false
}

variable "auto_minor_version_upgrade" {
  description = "Whether to automatically upgrade to new minor versions"
  type        = bool
  default     = true
}

variable "apply_immediately" {
  description = "Whether to apply changes immediately or during maintenance window"
  type        = bool
  default     = false # Best practice for production
}

variable "maintenance_window_start_time" {
  description = "Configuration for the maintenance window start time"
  type = object({
    day_of_week = string
    time_of_day = string
    time_zone   = string
  })
  default = {
    day_of_week = "SUNDAY"
    time_of_day = "02:00"
    time_zone   = "UTC"
  }
}

variable "kms_key_id" {
  description = "KMS key ID for encryption at rest"
  type        = string
  default     = null
}

variable "username" {
  description = "Username for broker access. Not required if using secrets_manager_secret_id."
  type        = string
  default     = "magento"
}

variable "create_secrets_manager_secret" {
  description = "Whether to create a new AWS Secrets Manager secret for AmazonMQ credentials. This is always true unless secrets_manager_secret_id is provided."
  type        = bool
  default     = true
}

variable "secrets_manager_secret_id" {
  description = "The ID of an existing AWS Secrets Manager secret containing AmazonMQ credentials. The secret must contain 'username' and 'password' keys."
  type        = string
  default     = null
}

variable "secrets_manager_kms_key_id" {
  description = "KMS Key ID for encrypting secrets stored in AWS Secrets Manager"
  type        = string
  default     = null
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "broker_configuration" {
  description = "AmazonMQ configuration data for Magento. If not provided, default configuration optimized for Magento will be used."
  type        = string
  default     = <<-EOF
    # Default RabbitMQ configuration with optimizations for Magento
    # Increase file descriptors limit
    total_memory_available_override_value = 0.8

    # Disk free limit - stop accepting messages when disk space drops below this
    disk_free_limit.absolute = 2GB

    # Memory threshold - flow control is triggered when memory usage exceeds this
    vm_memory_high_watermark.relative = 0.7

    # Increase default timeouts for better handling of large messages
    consumer_timeout = 3600000

    # Increase message size limit for large payloads
    max_message_size = 134217728
  EOF
}
