# EIP
resource "aws_eip" "this" {
  for_each = aws_subnet.public

  domain = "vpc"

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "eip", "natgw", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }
}

# NAT GW
resource "aws_nat_gateway" "this" {
  for_each = aws_subnet.public

  subnet_id     = each.value.id
  allocation_id = aws_eip.this[each.key].id

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "natgw", trimprefix(each.value.availability_zone, data.aws_region.this.name)])
  }

  depends_on = [aws_internet_gateway.this]
}
