resource "aws_security_group" "this" {
  name        = join("-", [var.program, var.project, var.environment, "lb", var.name, "sg"])
  description = "Security group for ${join("-", [var.program, var.project, var.environment, "lb", var.name])}"
  vpc_id      = var.vpc_id

  tags = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_vpc_security_group_ingress_rule" "ingres_80" {
  security_group_id = aws_security_group.this.id
  description       = "Allow all inbound traffic on 80 port to ${join("-", [var.program, var.project, var.environment, "lb", var.name])}"

  ip_protocol = "tcp"
  cidr_ipv4   = "0.0.0.0/0"
  from_port   = 80
  to_port     = 80
}

resource "aws_vpc_security_group_ingress_rule" "ingress_443" {
  security_group_id = aws_security_group.this.id
  description       = "Allow all inbound traffic on 443 port to ${join("-", [var.program, var.project, var.environment, "lb", var.name])}"

  ip_protocol = "tcp"
  cidr_ipv4   = "0.0.0.0/0"
  from_port   = 443
  to_port     = 443
}

#trivy:ignore:aws-vpc-no-public-egress-sgr
resource "aws_vpc_security_group_egress_rule" "egress" {
  security_group_id = aws_security_group.this.id
  description       = "Allow all outbound traffic"

  ip_protocol = "-1"
  cidr_ipv4   = "0.0.0.0/0"
  from_port   = 0
  to_port     = 0
}

#trivy:ignore:AVD-AWS-0053
resource "aws_lb" "this" {
  name               = join("-", [var.program, var.project, var.environment, "lb", var.name])
  internal           = var.internal
  load_balancer_type = var.load_balancer_type

  subnets         = var.subnets
  security_groups = aws_security_group.this.id

  idle_timeout               = var.idle_timeout
  ip_address_type            = var.ip_address_type
  drop_invalid_header_fields = var.drop_invalid_header_fields

  enable_http2                                = var.enable_http2
  enable_cross_zone_load_balancing            = var.enable_cross_zone_load_balancing
  enable_tls_version_and_cipher_suite_headers = var.enable_tls_version_and_cipher_suite_headers

  dynamic "access_logs" {
    for_each = toset([var.access_logs_bucket_id])
    content {
      enabled = true
      bucket  = access_logs.value
    }
  }

  tags = var.tags
}
