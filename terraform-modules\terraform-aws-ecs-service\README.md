# terraform-aws-ecs-service

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_ecs_service.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_service) | resource |
| [aws_lb_target_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_service_discovery_service.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/service_discovery_service) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_capacity_provider_strategy"></a> [capacity\_provider\_strategy](#input\_capacity\_provider\_strategy) | Capacity provider strategies to use for the service | <pre>list(object({<br/>    capacity_provider = string<br/>    weight            = number<br/>    base              = optional(number)<br/>  }))</pre> | `[]` | no |
| <a name="input_cluster_id"></a> [cluster\_id](#input\_cluster\_id) | ARN of the ECS cluster where the service will be deployed | `string` | n/a | yes |
| <a name="input_deployment_circuit_breaker_enabled"></a> [deployment\_circuit\_breaker\_enabled](#input\_deployment\_circuit\_breaker\_enabled) | Whether to enable deployment circuit breaker with automatic rollback | `bool` | `true` | no |
| <a name="input_deployment_maximum_percent"></a> [deployment\_maximum\_percent](#input\_deployment\_maximum\_percent) | Upper limit on the number of tasks that can be running during a deployment | `number` | `200` | no |
| <a name="input_deployment_minimum_healthy_percent"></a> [deployment\_minimum\_healthy\_percent](#input\_deployment\_minimum\_healthy\_percent) | Lower limit on the number of tasks that must remain in RUNNING state during a deployment | `number` | `100` | no |
| <a name="input_desired_count"></a> [desired\_count](#input\_desired\_count) | Number of instances of the task definition to place and keep running | `number` | `1` | no |
| <a name="input_enable_ecs_managed_tags"></a> [enable\_ecs\_managed\_tags](#input\_enable\_ecs\_managed\_tags) | Specifies whether to enable Amazon ECS managed tags for the tasks within the service | `bool` | `true` | no |
| <a name="input_enable_execute_command"></a> [enable\_execute\_command](#input\_enable\_execute\_command) | Specifies whether to enable Amazon ECS Exec for the tasks within the service | `bool` | `false` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment stage | `string` | n/a | yes |
| <a name="input_force_new_deployment"></a> [force\_new\_deployment](#input\_force\_new\_deployment) | Enable to force a new task deployment of the service | `bool` | `false` | no |
| <a name="input_health_check_grace_period_seconds"></a> [health\_check\_grace\_period\_seconds](#input\_health\_check\_grace\_period\_seconds) | Seconds to ignore failing load balancer health checks on newly instantiated tasks | `number` | `0` | no |
| <a name="input_launch_type"></a> [launch\_type](#input\_launch\_type) | Launch type on which to run your service. Valid values are EC2 and FARGATE | `string` | `"FARGATE"` | no |
| <a name="input_load_balancer"></a> [load\_balancer](#input\_load\_balancer) | List of load balancer rules for ECS service | <pre>list(object({<br/>    container_name    = string<br/>    container_port    = number<br/>    protocol          = string<br/>    health_check_path = string<br/>    target_group_arn  = optional(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_name"></a> [name](#input\_name) | Name of the ECS Service | `string` | n/a | yes |
| <a name="input_network_configuration"></a> [network\_configuration](#input\_network\_configuration) | Network configuration for the service | <pre>object({<br/>    subnets          = list(string)<br/>    security_groups  = list(string)<br/>    assign_public_ip = bool<br/>  })</pre> | n/a | yes |
| <a name="input_ordered_placement_strategy"></a> [ordered\_placement\_strategy](#input\_ordered\_placement\_strategy) | Service level strategy rules that are taken into consideration during task placement | <pre>list(object({<br/>    type  = string<br/>    field = string<br/>  }))</pre> | `[]` | no |
| <a name="input_placement_constraints"></a> [placement\_constraints](#input\_placement\_constraints) | Rules that are taken into consideration during task placement | <pre>list(object({<br/>    type       = string<br/>    expression = string<br/>  }))</pre> | `[]` | no |
| <a name="input_platform_version"></a> [platform\_version](#input\_platform\_version) | Platform version on which to run your service. Only applicable for launch\_type set to FARGATE | `string` | `"LATEST"` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | n/a | yes |
| <a name="input_propagate_tags"></a> [propagate\_tags](#input\_propagate\_tags) | Specifies whether to propagate the tags from the task definition or the service to the tasks | `string` | `"SERVICE"` | no |
| <a name="input_scheduling_strategy"></a> [scheduling\_strategy](#input\_scheduling\_strategy) | Scheduling strategy to use for the service. Valid values are REPLICA and DAEMON | `string` | `"REPLICA"` | no |
| <a name="input_service_discovery"></a> [service\_discovery](#input\_service\_discovery) | List of service discovery configurations. Supports both creation and reuse of Cloud Map services. | <pre>list(object({<br/>    container_name = string<br/>    container_port = number<br/><br/>    # If creating a new service<br/>    namespace_id   = optional(string)<br/>    dns_ttl        = optional(number)<br/>    routing_policy = optional(string) # Valid values: WEIGHTED or MULTIVALUE<br/><br/>    # If using an existing Cloud Map service<br/>    registry_arn = optional(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to assign to resources | `map(string)` | `{}` | no |
| <a name="input_task_definition_arn"></a> [task\_definition\_arn](#input\_task\_definition\_arn) | ARN of the task definition that defines the containers to be deployed | `string` | n/a | yes |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | ID of the VPC used for load balancer target groups and service discovery | `string` | n/a | yes |
| <a name="input_wait_for_steady_state"></a> [wait\_for\_steady\_state](#input\_wait\_for\_steady\_state) | Whether Terraform should wait for the service to reach a steady state | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_target_group_arns"></a> [target\_group\_arns](#output\_target\_group\_arns) | Map of created Target Group ARNs |
| <a name="output_this_ecs_service_cluster"></a> [this\_ecs\_service\_cluster](#output\_this\_ecs\_service\_cluster) | The ARN of the ECS cluster where the service is deployed |
| <a name="output_this_ecs_service_desired_count"></a> [this\_ecs\_service\_desired\_count](#output\_this\_ecs\_service\_desired\_count) | The desired count of the ECS service |
| <a name="output_this_ecs_service_id"></a> [this\_ecs\_service\_id](#output\_this\_ecs\_service\_id) | The ID of the ECS service |
| <a name="output_this_ecs_service_launch_type"></a> [this\_ecs\_service\_launch\_type](#output\_this\_ecs\_service\_launch\_type) | The launch type of the ECS service |
| <a name="output_this_ecs_service_name"></a> [this\_ecs\_service\_name](#output\_this\_ecs\_service\_name) | The name of the ECS service |
| <a name="output_this_ecs_service_task_definition"></a> [this\_ecs\_service\_task\_definition](#output\_this\_ecs\_service\_task\_definition) | The task definition used by the ECS service |
<!-- END_TF_DOCS -->
