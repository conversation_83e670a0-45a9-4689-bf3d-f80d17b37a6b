# VPN configuration - Based on networking team specifications
# Primary customer gateway IP (Spectrum connection)
customer_gateway_ip        = "***********"  # Ripon Public IP (Spectrum)
bgp_asn                    = 65000
vpn_static_routes_only     = true

# Phase 2 Selectors - On-premises networks that AWS DEV will route to via VPN
vpn_destination_cidr_blocks = [
  "***************/32"   # RP01ERD01 (DEV Local only)
]

# Placeholder SNS ARNs - Update with actual ARNs when available
vpn_alarm_actions          = []  # ["arn:aws:sns:us-east-2:ACCOUNT:vpn-alerts"]
vpn_ok_actions             = []  # ["arn:aws:sns:us-east-2:ACCOUNT:vpn-alerts"]

# Note: Second customer gateway IP for redundancy: ************ (Bright Speed)
# This would require a second VPN connection for true redundancy