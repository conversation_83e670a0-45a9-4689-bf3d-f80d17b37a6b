locals {
  full_name = join("-", [var.program, var.project, var.environment, var.name])
}

resource "aws_cloudwatch_log_group" "this" {
  count = var.create_cloudwatch_log_group ? 1 : 0

  name              = "/aws/ecs/${local.full_name}"
  retention_in_days = var.cloudwatch_log_retention_days
}

module "task" {
  source = "../../terraform-modules/terraform-aws-ecs-task"

  program     = var.program
  project     = var.project
  environment = var.environment

  name = var.name

  # cluster_id = var.cluster_id
  cpu                   = tostring(var.cpu)
  memory                = tostring(var.memory)
  container_definitions = var.container_definitions
  execution_role_arn    = var.execution_role_arn

  # task_definition = {
  #   family                   = local.full_name
  #   network_mode             = "awsvpc"
  #   requires_compatibilities = [var.launch_type]

  #   task_role_arn            = var.task_role_arn
  #   execution_role_arn       = var.execution_role_arn

  #   runtime_platform = var.runtime_platform

  # }
}

resource "aws_service_discovery_service" "this" {
  count = var.enable_service_discovery ? 1 : 0

  name = var.name

  dns_config {
    namespace_id = var.service_discovery_namespace_id

    dns_records {
      ttl  = 10
      type = "A"
    }

    routing_policy = "MULTIVALUE"
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}

module "service" {
  source = "../../terraform-modules/terraform-aws-ecs-service"

  program     = var.program
  project     = var.project
  environment = var.environment


  vpc_id = var.vpc_id

  name = var.name

  cluster_id          = var.cluster_id
  task_definition_arn = module.task.this_task_definition_arn
  desired_count       = var.desired_count
  launch_type         = var.launch_type
  platform_version    = var.launch_type == "FARGATE" ? var.platform_version : null

  network_configuration = {
    subnets          = var.subnet_ids
    security_groups  = var.security_group_ids
    assign_public_ip = var.assign_public_ip
  }

  # service_registries = var.enable_service_discovery ? [
  #   {
  #     registry_arn = aws_service_discovery_service.this[0].arn
  #   }
  # ] : []

  # load_balancer = var.load_balancer_config

  # deployment_configuration = {
  #   maximum_percent         = var.deployment_maximum_percent
  #   minimum_healthy_percent = var.deployment_minimum_healthy_percent
  # }

  health_check_grace_period_seconds = var.health_check_grace_period_seconds

  enable_ecs_managed_tags = var.enable_ecs_managed_tags
  propagate_tags          = var.propagate_tags
}