output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.fargate.cluster_id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = module.fargate.cluster_arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = module.fargate.cluster_name
}

output "task_execution_role_arn" {
  description = "The ARN of the IAM role created for ECS task execution"
  value       = module.fargate.task_execution_role_arn
}

output "task_execution_role_name" {
  description = "The name of the IAM role created for ECS task execution"
  value       = module.fargate.task_execution_role_name
}

output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.network.vpc.this_vpc_id
}

output "protected_subnet_ids" {
  description = "List of protected subnet IDs"
  value       = module.network.vpc.protected_subnet_ids
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.network.vpc.private_subnet_ids
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.network.vpc.public_subnet_ids
}

# VPN Outputs
output "vpn_primary_tunnel1_address" {
  description = "AWS tunnel IP for Spectrum ISP (primary tunnel)"
  value       = module.network.vpn_primary_tunnel1_address
}

output "vpn_secondary_tunnel1_address" {
  description = "AWS tunnel IP for Bright Speed ISP (primary tunnel)"
  value       = module.network.vpn_secondary_tunnel1_address
}

output "vpn_primary_tunnel2_address" {
  description = "AWS tunnel IP for Spectrum ISP (backup tunnel)"
  value       = module.network.vpn_primary_tunnel2_address
}

output "vpn_secondary_tunnel2_address" {
  description = "AWS tunnel IP for Bright Speed ISP (backup tunnel)"
  value       = module.network.vpn_secondary_tunnel2_address
}

output "vpn_primary_connection_id" {
  description = "VPN Connection ID for Spectrum ISP"
  value       = module.network.vpn_primary_connection_id
}

output "vpn_secondary_connection_id" {
  description = "VPN Connection ID for Bright Speed ISP"
  value       = module.network.vpn_secondary_connection_id
}