output "this_cluster_id" {
  description = "The ID of the ECS cluster"
  value       = try(aws_ecs_cluster.this.id, null)
}

output "this_cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = try(aws_ecs_cluster.this.arn, null)
}

output "this_cluster_name" {
  description = "The name of the ECS cluster"
  value       = try(aws_ecs_cluster.this.name, null)
}

output "this_task_execution_role_arn" {
  description = "The ARN of the IAM role created for ECS task execution"
  value       = try(aws_iam_role.this[0].arn, null)
}

output "this_task_execution_role_name" {
  description = "The name of the IAM role created for ECS task execution"
  value       = try(aws_iam_role.this[0].name, null)
}
