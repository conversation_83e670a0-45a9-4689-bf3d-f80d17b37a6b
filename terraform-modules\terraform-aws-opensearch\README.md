# terraform-aws-opensearch


<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.94 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.94 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_log_group.opensearch_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_cloudwatch_log_resource_policy.opensearch_log_publishing_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_resource_policy) | resource |
| [aws_iam_service_linked_role.opensearch](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_service_linked_role) | resource |
| [aws_opensearch_domain.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/opensearch_domain) | resource |
| [aws_security_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_vpc_security_group_egress_rule.egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_iam_policy_document.cloudwatch_log_publishing_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.opensearch_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_subnet.subnets](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnet) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_access_policy_conditions"></a> [access\_policy\_conditions](#input\_access\_policy\_conditions) | List of condition blocks to apply to the access policy. Each condition block should have 'test', 'variable', and 'values' keys. Set to empty list for no conditions. | <pre>list(object({<br/>    test     = string<br/>    variable = string<br/>    values   = list(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_advanced_options"></a> [advanced\_options](#input\_advanced\_options) | Key-value string pairs to specify advanced configuration options | `map(string)` | <pre>{<br/>  "rest.action.multi.allow_explicit_index": "true"<br/>}</pre> | no |
| <a name="input_automated_snapshot_start_hour"></a> [automated\_snapshot\_start\_hour](#input\_automated\_snapshot\_start\_hour) | Hour during which the service takes an automated daily snapshot of the indices | `number` | `0` | no |
| <a name="input_create_service_linked_role"></a> [create\_service\_linked\_role](#input\_create\_service\_linked\_role) | Whether to create the service linked role for OpenSearch service | `bool` | `true` | no |
| <a name="input_ebs_volume_size"></a> [ebs\_volume\_size](#input\_ebs\_volume\_size) | Size of EBS volumes attached to data nodes (in GB) | `number` | `10` | no |
| <a name="input_ebs_volume_type"></a> [ebs\_volume\_type](#input\_ebs\_volume\_type) | Type of EBS volumes attached to data nodes | `string` | `"gp3"` | no |
| <a name="input_enable_logs"></a> [enable\_logs](#input\_enable\_logs) | Whether to enable CloudWatch logging for the OpenSearch domain. If true, creates CloudWatch log groups and configures all OpenSearch logs to be sent to CloudWatch. | `bool` | `false` | no |
| <a name="input_encrypt_at_rest"></a> [encrypt\_at\_rest](#input\_encrypt\_at\_rest) | Whether to enable encryption at rest | `bool` | `true` | no |
| <a name="input_enforce_https"></a> [enforce\_https](#input\_enforce\_https) | Whether to require HTTPS for all traffic to the domain | `bool` | `true` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment stage | `string` | n/a | yes |
| <a name="input_instance_count"></a> [instance\_count](#input\_instance\_count) | Number of instances in the cluster | `number` | `1` | no |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | Instance type for OpenSearch data nodes | `string` | `"t3.small.search"` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS key ID to encrypt the OpenSearch domain with. If not specified, the default AWS KMS key for OpenSearch will be used. | `string` | `null` | no |
| <a name="input_name"></a> [name](#input\_name) | Name of the OpenSearch domain | `string` | `"magento-opensearch"` | no |
| <a name="input_node_to_node_encryption"></a> [node\_to\_node\_encryption](#input\_node\_to\_node\_encryption) | Whether to enable node-to-node encryption | `bool` | `true` | no |
| <a name="input_opensearch_version"></a> [opensearch\_version](#input\_opensearch\_version) | Version of OpenSearch to deploy | `string` | `"2.5"` | no |
| <a name="input_program"></a> [program](#input\_program) | program name | `string` | n/a | yes |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | n/a | yes |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | List of subnet IDs for the OpenSearch domain | `list(string)` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |
| <a name="input_tls_security_policy"></a> [tls\_security\_policy](#input\_tls\_security\_policy) | Security policy for TLS | `string` | `"Policy-Min-TLS-1-2-2019-07"` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | ID of the VPC where to create the OpenSearch domain | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_this_dashboard_endpoint"></a> [this\_dashboard\_endpoint](#output\_this\_dashboard\_endpoint) | Domain-specific endpoint for OpenSearch Dashboards (Kibana) with https scheme |
| <a name="output_this_domain_arn"></a> [this\_domain\_arn](#output\_this\_domain\_arn) | ARN of the OpenSearch domain |
| <a name="output_this_domain_endpoint"></a> [this\_domain\_endpoint](#output\_this\_domain\_endpoint) | Domain-specific endpoint used to submit index, search, and data upload requests |
| <a name="output_this_domain_id"></a> [this\_domain\_id](#output\_this\_domain\_id) | ID of the OpenSearch domain |
| <a name="output_this_domain_name"></a> [this\_domain\_name](#output\_this\_domain\_name) | Name of the OpenSearch domain |
| <a name="output_this_engine_version"></a> [this\_engine\_version](#output\_this\_engine\_version) | Version of OpenSearch used |
| <a name="output_this_kibana_endpoint"></a> [this\_kibana\_endpoint](#output\_this\_kibana\_endpoint) | Domain-specific endpoint for Kibana without https scheme |
| <a name="output_this_log_group_arns"></a> [this\_log\_group\_arns](#output\_this\_log\_group\_arns) | ARNs of the CloudWatch log groups created for OpenSearch logs (only if enable\_logs is true) |
| <a name="output_this_security_group_id"></a> [this\_security\_group\_id](#output\_this\_security\_group\_id) | ID of the security group created for the OpenSearch domain |
<!-- END_TF_DOCS -->
