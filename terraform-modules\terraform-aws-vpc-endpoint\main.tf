resource "aws_vpc_endpoint" "this" {
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.${var.service_name}"
  vpc_endpoint_type   = var.endpoint_type
  private_dns_enabled = var.endpoint_type == "Interface" ? var.private_dns_enabled : null
  policy              = var.policy

  tags = merge(
    {
      Name = join("-", [var.program, var.project, var.environment, "vpc-endpoint", var.service_name])
    }
  )
}

resource "aws_vpc_endpoint_subnet_association" "this" {
  for_each = var.endpoint_type == "Interface" ? toset(var.subnet_ids) : []

  vpc_endpoint_id = aws_vpc_endpoint.this.id
  subnet_id       = each.value
}

resource "aws_vpc_endpoint_route_table_association" "this" {
  for_each = var.endpoint_type == "Gateway" ? toset(var.route_table_ids) : []

  vpc_endpoint_id = aws_vpc_endpoint.this.id
  route_table_id  = each.value
}

resource "aws_security_group" "this" {
  count       = var.endpoint_type == "Interface" ? 1 : 0
  name        = join("-", [var.program, var.project, var.environment, "vpc-endpoint", var.service_name, "sg"])
  description = "Security group for VPC Endpoint of ${var.service_name} service"
  vpc_id      = var.vpc_id

}

resource "aws_security_group_rule" "ingress" {
  for_each = var.endpoint_type == "Interface" ? data.aws_subnet.subnets : {}

  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = [each.value.cidr_block]
  security_group_id = aws_security_group.this[0].id
  description       = "Allow HTTPS from ${each.value.cidr_block}"
}

#trivy:ignore:aws-vpc-no-public-egress-sgr
resource "aws_security_group_rule" "egress" {
  count             = var.endpoint_type == "Interface" ? 1 : 0
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.this[0].id
  description       = "Allow all outbound traffic"
}

resource "aws_vpc_endpoint_security_group_association" "this" {
  count             = var.endpoint_type == "Interface" ? 1 : 0
  vpc_endpoint_id   = aws_vpc_endpoint.this.id
  security_group_id = aws_security_group.this[0].id
}
