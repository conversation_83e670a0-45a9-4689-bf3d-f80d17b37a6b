variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment stage"
  type        = string
}

variable "name" {
  description = "Name for the ECS cluster"
  type        = string
  default     = "ecs"
}


variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "container_insights" {
  description = "Enable container insights for the ECS cluster"
  type        = bool
  default     = true
}

variable "create_ecs_service_linked_role" {
  description = "Whether to create the AWSServiceRoleForECS service-linked role"
  type        = bool
  default     = true
}

variable "create_task_execution_role" {
  description = "Whether to create the ECS task execution IAM role"
  type        = bool
  default     = true
}
