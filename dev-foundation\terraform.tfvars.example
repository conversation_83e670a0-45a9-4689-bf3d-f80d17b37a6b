# VPN configuration - Based on networking team specifications
# Copy this file to terraform.tfvars and update with actual values

# Primary customer gateway IP (Spectrum connection)
customer_gateway_ip        = "YOUR_CUSTOMER_GATEWAY_IP"  # Example: ***********
bgp_asn                    = 65000
vpn_static_routes_only     = true

# Phase 2 Selectors - On-premises networks that AWS DEV will route to via VPN
vpn_destination_cidr_blocks = [
  "YOUR_ONPREM_DEV_IP/32"  # Example: RP01ERD01 (DEV Local only)
]

# SNS ARNs for VPN monitoring - Update with actual ARNs when available
vpn_alarm_actions          = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]
vpn_ok_actions             = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]

# Note: For redundancy, consider setting up a second VPN connection
# with the secondary customer gateway IP
