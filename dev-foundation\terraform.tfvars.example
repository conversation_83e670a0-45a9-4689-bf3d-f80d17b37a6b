# VPN configuration - Based on networking team specifications
# Copy this file to terraform.tfvars and update with actual values

# Primary customer gateway IP (Spectrum connection)
customer_gateway_ip        = "YOUR_CUSTOMER_GATEWAY_IP"  # Example: ***********
bgp_asn                    = 65000
vpn_static_routes_only     = true

# Phase 2 Selectors - On-premises networks that AWS will route to via VPN
vpn_destination_cidr_blocks = [
  "YOUR_ONPREM_IP_1/32",  # Example: RP01ERD01 (DEV Local)
  "YOUR_ONPREM_IP_2/32",  # Example: RP01ERP01 (PROD Local) 
  "YOUR_ONPREM_IP_3/32"   # Example: RP01ERQ01 (SIT Local)
]

# SNS ARNs for VPN monitoring - Update with actual ARNs when available
vpn_alarm_actions          = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]
vpn_ok_actions             = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]

# Note: For redundancy, consider setting up a second VPN connection
# with the secondary customer gateway IP
