# VPN configuration - Dual customer gateway setup per networking team request
# Copy this file to dev.auto.tfvars and update with actual values

# Dual customer gateway IPs for redundancy
customer_gateway_ip_1      = "YOUR_SPECTRUM_IP"      # Example: ***********
customer_gateway_ip_2      = "YOUR_BRIGHT_SPEED_IP"  # Example: ************
bgp_asn                    = 65000
vpn_static_routes_only     = true

# Phase 2 Selectors - DEV environment only
vpn_destination_cidr_blocks = [
  "YOUR_ONPREM_DEV_IP/32"  # Example: RP01ERD01 (DEV Local only)
]

# SNS ARNs for VPN monitoring - Update with actual ARNs when available
vpn_alarm_actions          = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]
vpn_ok_actions             = []  # ["arn:aws:sns:us-east-2:ACCOUNT_ID:vpn-alerts"]

# Note: For redundancy, consider setting up a second VPN connection
# with the secondary customer gateway IP
