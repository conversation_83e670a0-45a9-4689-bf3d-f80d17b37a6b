# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is an Evolution Infrastructure as Code (IaC) repository using OpenTofu (Terraform-compatible) to manage AWS infrastructure. The codebase follows a modular architecture pattern for provisioning infrastructure across multiple environments and applications.

## Common Development Commands

### OpenTofu/Terraform Commands
```bash
# Initialize working directory
tofu init

# Format code to canonical format
tofu fmt -recursive

# Validate configuration
tofu validate

# Plan infrastructure changes
tofu plan

# Apply infrastructure changes
tofu apply

# Destroy infrastructure
tofu destroy
```

### Documentation Generation
```bash
# Generate module documentation (from within a module directory)
terraform-docs markdown . > README.md
```

### AWS Profile Configuration
The repository uses AWS profile `terraform` by default. Ensure your AWS credentials are configured:
```bash
aws configure --profile terraform
```

## Architecture and Structure

### Environment Organization
- `dev-foundation/` - Core networking and foundational infrastructure
- `dev-axon/` - Axon application infrastructure
- `dev-magento/` - Magento e-commerce platform infrastructure
- `dev-pimcore/` - Pimcore CMS/PIM infrastructure

### Terraform Modules (`terraform-modules/`)
Reusable infrastructure components following consistent patterns:
- Each module has: `main.tf`, `variables.tf`, `outputs.tf`, `data.tf`, `versions.tf`
- Documentation auto-generated using terraform-docs markers
- Modules are prefixed with `terraform-aws-` followed by the AWS service

### Skeletons
Reusable infrastructure components:
- If a module represents a brick when building a house, a skeleton would represent a wall. This is an abstraction to piece together multiple modules.

### State Management
- Backend: S3 with DynamoDB locking
- State files stored in S3 bucket created by `state-setup/`
- Region: `us-east-2`
- Shared across all dev environment projects

### Network Architecture
Three-tier subnet design:
- **Public subnets**: Internet-facing resources (NAT gateways, load balancers)
- **Private subnets**: Application layer (ECS, EC2)
- **Protected subnets**: Data layer (RDS, ElastiCache)

### Tagging Strategy
All resources must include:
```hcl
tags = {
  Program     = "evolution"
  Project     = "<component-name>" # e.g., foundation, axon, magento, pimcore
  Environment = "dev"
  Team        = "enterprise-digitalization"
}
```

## Development Environment

The repository includes a Dev Container configuration with:
- OpenTofu v1.9.1
- AWS CLI with automatic credential mounting from `~/.aws`
- Default AWS profile: `terraform`
- Default region: `us-east-2`

To use the Dev Container:
1. Open project in Cursor or VS Code
2. Select "Reopen in Container" when prompted
3. AWS credentials are automatically mounted

## Module Development Guidelines

When creating or modifying modules:
1. Follow the existing module structure pattern
2. Include comprehensive variable descriptions
3. Add outputs for all created resource IDs and ARNs
4. Use data sources for existing resources rather than hardcoding
5. Enable deletion protection on critical resources
6. Document all variables and outputs for terraform-docs generation

## Security Considerations

- Never commit `.tfvars` files (they may contain sensitive data)
- Use KMS encryption for all data at rest
- Implement VPC endpoints for private AWS service connectivity
- Define security groups with explicit rule configurations
- Enable deletion protection on production-critical resources