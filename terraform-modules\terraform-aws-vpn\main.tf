# AWS Customer Gateway
resource "aws_customer_gateway" "this" {
  bgp_asn    = var.bgp_asn
  ip_address = var.customer_gateway_ip
  type       = "ipsec.1"

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "cgw"])
  }
}

# AWS VPN Gateway
resource "aws_vpn_gateway" "this" {
  vpc_id = var.vpc_id

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "vgw"])
  }

  lifecycle {
    create_before_destroy = true
  }
}

# VPN Gateway Attachment
resource "aws_vpn_gateway_attachment" "this" {
  vpc_id         = var.vpc_id
  vpn_gateway_id = aws_vpn_gateway.this.id
}

# Ensure VPN Gateway is properly associated with route tables
resource "aws_vpn_gateway_route_propagation" "this" {
  count = var.enable_route_propagation ? 1 : 0

  vpn_gateway_id = aws_vpn_gateway.this.id
  route_table_id = var.route_table_id
}

# AWS VPN Connection
resource "aws_vpn_connection" "this" {
  vpn_gateway_id      = aws_vpn_gateway.this.id
  customer_gateway_id = aws_customer_gateway.this.id
  type                = "ipsec.1"
  static_routes_only  = var.static_routes_only

  tunnel1_inside_ipv6_cidr = var.tunnel1_inside_ipv6_cidr
  tunnel2_inside_ipv6_cidr = var.tunnel2_inside_ipv6_cidr

  tunnel1_phase1_dh_group_numbers      = var.tunnel1_phase1_dh_group_numbers
  tunnel1_phase1_encryption_algorithms = var.tunnel1_phase1_encryption_algorithms
  tunnel1_phase1_integrity_algorithms  = var.tunnel1_phase1_integrity_algorithms
  tunnel1_phase1_lifetime_seconds      = var.tunnel1_phase1_lifetime_seconds
  tunnel1_phase2_dh_group_numbers      = var.tunnel1_phase2_dh_group_numbers
  tunnel1_phase2_encryption_algorithms = var.tunnel1_phase2_encryption_algorithms
  tunnel1_phase2_integrity_algorithms  = var.tunnel1_phase2_integrity_algorithms
  tunnel1_phase2_lifetime_seconds      = var.tunnel1_phase2_lifetime_seconds
  tunnel1_rekey_fuzz_percentage        = var.tunnel1_rekey_fuzz_percentage
  tunnel1_rekey_margin_time_seconds    = var.tunnel1_rekey_margin_time_seconds
  tunnel1_replay_window_size           = var.tunnel1_replay_window_size
  tunnel1_startup_action               = var.tunnel1_startup_action

  tunnel2_phase1_dh_group_numbers      = var.tunnel2_phase1_dh_group_numbers
  tunnel2_phase1_encryption_algorithms = var.tunnel2_phase1_encryption_algorithms
  tunnel2_phase1_integrity_algorithms  = var.tunnel2_phase1_integrity_algorithms
  tunnel2_phase1_lifetime_seconds      = var.tunnel2_phase1_lifetime_seconds
  tunnel2_phase2_dh_group_numbers      = var.tunnel2_phase2_dh_group_numbers
  tunnel2_phase2_encryption_algorithms = var.tunnel2_phase2_encryption_algorithms
  tunnel2_phase2_integrity_algorithms  = var.tunnel2_phase2_integrity_algorithms
  tunnel2_phase2_lifetime_seconds      = var.tunnel2_phase2_lifetime_seconds
  tunnel2_rekey_fuzz_percentage        = var.tunnel2_rekey_fuzz_percentage
  tunnel2_rekey_margin_time_seconds    = var.tunnel2_rekey_margin_time_seconds
  tunnel2_replay_window_size           = var.tunnel2_replay_window_size
  tunnel2_startup_action               = var.tunnel2_startup_action

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "vpn"])
  }

  lifecycle {
    create_before_destroy = true
  }
}

# VPN Connection Routes (if using static routing)
resource "aws_vpn_connection_route" "this" {
  for_each = toset(var.destination_cidr_blocks)

  destination_cidr_block = each.value
  vpn_connection_id      = aws_vpn_connection.this.id
}

# Route Table Association
resource "aws_route" "vpn" {
  for_each = toset(var.destination_cidr_blocks)

  route_table_id         = var.route_table_id
  destination_cidr_block = each.value
  gateway_id             = aws_vpn_gateway.this.id
}

# CloudWatch Alarm for VPN Tunnel Status
resource "aws_cloudwatch_metric_alarm" "vpn_tunnel_status" {
  for_each = {
    tunnel1 = "TunnelState"
    tunnel2 = "TunnelState"
  }

  alarm_name          = join("-", [var.program, var.project, var.environment, "vpn", each.key, "status"])
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "1"
  metric_name         = each.value
  namespace           = "AWS/VPN"
  period              = "60"
  statistic           = "Minimum"
  threshold           = "1"
  alarm_description   = "VPN tunnel ${each.key} is down"
  alarm_actions       = var.alarm_actions
  ok_actions          = var.ok_actions

  dimensions = {
    VpnId           = aws_vpn_connection.this.id
    TunnelIpAddress = each.key == "tunnel1" ? aws_vpn_connection.this.tunnel1_address : aws_vpn_connection.this.tunnel2_address
  }

  tags = {
    Name = join("-", [var.program, var.project, var.environment, "vpn", each.key, "alarm"])
  }
} 
