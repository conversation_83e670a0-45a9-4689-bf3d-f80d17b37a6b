output "this_domain_id" {
  description = "ID of the OpenSearch domain"
  value       = aws_opensearch_domain.this.domain_id
}

output "this_domain_name" {
  description = "Name of the OpenSearch domain"
  value       = aws_opensearch_domain.this.domain_name
}

output "this_domain_arn" {
  description = "ARN of the OpenSearch domain"
  value       = aws_opensearch_domain.this.arn
}

output "this_domain_endpoint" {
  description = "Domain-specific endpoint used to submit index, search, and data upload requests"
  value       = aws_opensearch_domain.this.endpoint
}

output "this_kibana_endpoint" {
  description = "Domain-specific endpoint for Kibana without https scheme"
  value       = aws_opensearch_domain.this.kibana_endpoint
}

output "this_security_group_id" {
  description = "ID of the security group created for the OpenSearch domain"
  value       = aws_security_group.this.id
}

output "this_dashboard_endpoint" {
  description = "Domain-specific endpoint for OpenSearch Dashboards (Kibana) with https scheme"
  value       = "https://${aws_opensearch_domain.this.kibana_endpoint}"
}

output "this_engine_version" {
  description = "Version of OpenSearch used"
  value       = aws_opensearch_domain.this.engine_version
}

output "this_log_group_arns" {
  description = "ARNs of the CloudWatch log groups created for OpenSearch logs (only if enable_logs is true)"
  value       = var.enable_logs ? { for k, v in aws_cloudwatch_log_group.opensearch_logs : k => v.arn } : {}
}
