# Terraform Skeleton ECS Task

This skeleton provides a reusable pattern for deploying ECS tasks and services.

## Features

- Creates ECS task definition and service
- Supports both Fargate and EC2 launch types
- Optional CloudWatch log group creation
- Optional service discovery integration
- Load balancer support
- Configurable deployment settings

## Usage

```hcl
module "my_task" {
  source = "../skeletons/terraform-skeleton-fargate-task"

  program     = var.program
  project     = var.project
  environment = var.environment

  name               = "my-service"
  cluster_id         = data.terraform_remote_state.foundation.outputs.fargate.cluster_id
  vpc_id             = data.terraform_remote_state.foundation.outputs.network.vpc.this_vpc_id
  subnet_ids         = data.terraform_remote_state.foundation.outputs.network.vpc.private_subnet_ids
  task_role_arn      = aws_iam_role.task_role.arn
  execution_role_arn = aws_iam_role.task_execution_role.arn
  
  cpu    = 2048
  memory = 4096

  container_definitions = jsonencode([
    {
      name  = "my-container"
      image = "my-image:latest"
      # ... container configuration
    }
  ])

  security_group_ids = [aws_security_group.my_service.id]
  desired_count      = 2
}
```

<!-- BEGIN_TF_DOCS -->
<!-- END_TF_DOCS -->