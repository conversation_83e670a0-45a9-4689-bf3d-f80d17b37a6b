# Rds Proxy
output "this_db_proxy_id" {
  description = "The ID of the RDS Proxy"
  value       = aws_db_proxy.this.id
}

output "this_db_proxy_arn" {
  description = "The ARN of the RDS Proxy"
  value       = aws_db_proxy.this.arn
}

# RDS Proxy Endpoints
output "read_write_endpoint" {
  description = "The endpoint of the RDS Proxy for read-write operations"
  value       = aws_db_proxy_endpoint.read_write.endpoint
}

output "read_only_endpoint" {
  description = "The endpoint of the RDS Proxy for read-only operations"
  value       = aws_db_proxy_endpoint.read_only.endpoint
}

# IAM role
output "iam_role_arn" {
  description = "The Amazon Resource Name (ARN) of the IAM role that the proxy uses to access secrets in AWS Secrets Manager."
  value       = try(aws_iam_role.this.arn, null)
}

output "iam_role_name" {
  description = "IAM role name"
  value       = try(aws_iam_role.this.name, null)
}

output "iam_role_unique_id" {
  description = "Stable and unique string identifying the IAM role"
  value       = try(aws_iam_role.this.unique_id, null)
}