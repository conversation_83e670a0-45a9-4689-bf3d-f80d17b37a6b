variable "program" {
  description = "program name"
  type        = string
}

variable "project" {
  description = "project name"
  type        = string
}

variable "environment" {
  description = "environment stage"
  type        = string
}

variable "repository_name" {
  description = "ECR name"
  type        = string
}

variable "kms_key_id" {
  description = "KMS ECS Key ID"
  type        = string
}

variable "enable_image_scanning" {
  description = "Enable image scanning"
  type        = bool
  default     = true
}

variable "read_only_arns" {
  description = "List of ARNs that can pull images - resource (aws_ecr_repository_policy)"
  type        = list(string)
}

variable "write_arns" {
  description = "List of ARNs that can push images - resource (aws_ecr_repository_policy)"
  type        = list(string)
}

variable "image_count" {
  description = "image count - resource (aws_ecr_lifecycle_policy)"
  type        = number
  default     = 10
}

variable "expire_images" {
  description = "expire images - resource (aws_ecr_lifecycle_policy)"
  type        = number
  default     = 30
}

variable "prefix_tag" {
  description = "list prefix tag - resource (aws_ecr_lifecycle_policy)"
  type        = list(string)
  default     = ["MAGENTO_", "PWA_"]
}

variable "image_tag_mutability" {
  description = "image tag mutability"
  type        = string
  default     = "IMMUTABLE"
}
