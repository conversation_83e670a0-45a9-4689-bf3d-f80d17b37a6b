FROM mcr.microsoft.com/devcontainers/base:ubuntu

# Install dependencies
RUN apt-get update && \
    apt-get install -y wget unzip curl && \
    curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws/ && \
    wget https://github.com/opentofu/opentofu/releases/download/v1.9.1/tofu_1.9.1_linux_arm64.zip && \
    unzip tofu_1.9.1_linux_arm64.zip && \
    mv tofu /usr/local/bin/ && \
    rm tofu_1.9.1_linux_arm64.zip && \
    wget https://github.com/terraform-docs/terraform-docs/releases/download/v0.18.0/terraform-docs-v0.18.0-linux-arm64.tar.gz && \
    tar -xzf terraform-docs-v0.18.0-linux-arm64.tar.gz && \
    chmod +x terraform-docs && \
    mv terraform-docs /usr/local/bin/ && \
    rm terraform-docs-v0.18.0-linux-arm64.tar.gz && \
    apt-get clean && rm -rf /var/lib/apt/lists/*