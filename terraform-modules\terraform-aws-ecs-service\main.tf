resource "aws_ecs_service" "this" {
  name                               = join("-", [var.program, var.project, var.environment, var.name, "ecs-service"])
  cluster                            = var.cluster_id
  task_definition                    = var.task_definition_arn
  desired_count                      = var.desired_count
  deployment_maximum_percent         = var.deployment_maximum_percent
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent
  enable_execute_command             = var.enable_execute_command
  health_check_grace_period_seconds  = try(var.load_balancer[0].target_group_arn, null) != null ? var.health_check_grace_period_seconds : null
  launch_type                        = length(var.capacity_provider_strategy) == 0 ? var.launch_type : null
  platform_version = var.platform_version != null && length([
    for cp in concat([var.launch_type], [for c in var.capacity_provider_strategy : c.capacity_provider]) :
    cp if contains(["FARGATE", "FARGATE_SPOT"], cp)
  ]) > 0 ? var.platform_version : null
  scheduling_strategy           = var.scheduling_strategy
  availability_zone_rebalancing = "ENABLED"
  force_new_deployment          = var.force_new_deployment
  wait_for_steady_state         = var.wait_for_steady_state
  propagate_tags                = var.propagate_tags
  enable_ecs_managed_tags       = var.enable_ecs_managed_tags
  tags                          = var.tags

  dynamic "network_configuration" {
    for_each = can(var.network_configuration.subnets) && length(var.network_configuration.subnets) > 0 ? [1] : []
    content {
      subnets          = var.network_configuration.subnets
      security_groups  = var.network_configuration.security_groups
      assign_public_ip = var.network_configuration.assign_public_ip
    }
  }

  dynamic "load_balancer" {
    for_each = {
      for lb in var.load_balancer :
      "${lb.container_name}-${lb.container_port}" => lb
    }
    content {
      target_group_arn = contains(keys(load_balancer.value), "target_group_arn") ? load_balancer.value.target_group_arn : aws_lb_target_group.this[load_balancer.key].arn

      container_name = load_balancer.value.container_name
      container_port = load_balancer.value.container_port
    }
  }

  dynamic "service_registries" {
    for_each = {
      for sd in var.service_discovery :
      "${sd.container_name}-${sd.container_port}" => sd
    }
    content {
      registry_arn = contains(keys(service_registries.value), "registry_arn") ? service_registries.value.registry_arn : aws_service_discovery_service.this[service_registries.key].arn

      container_name = service_registries.value.container_name
      container_port = service_registries.value.container_port
    }
  }

  dynamic "ordered_placement_strategy" {
    for_each = var.ordered_placement_strategy
    content {
      type  = ordered_placement_strategy.value.type
      field = ordered_placement_strategy.value.field
    }
  }

  dynamic "placement_constraints" {
    for_each = var.placement_constraints
    content {
      type       = placement_constraints.value.type
      expression = placement_constraints.value.expression
    }
  }

  dynamic "capacity_provider_strategy" {
    for_each = var.capacity_provider_strategy
    content {
      capacity_provider = capacity_provider_strategy.value.capacity_provider
      weight            = capacity_provider_strategy.value.weight
      base              = capacity_provider_strategy.value.base
    }
  }

  dynamic "deployment_circuit_breaker" {
    for_each = var.deployment_circuit_breaker_enabled ? [1] : [0]
    content {
      enable   = true
      rollback = true
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [task_definition]
  }
}

resource "aws_lb_target_group" "this" {
  for_each = {
    for lb in var.load_balancer :
    "${lb.container_name}-${lb.container_port}" => lb
    if !contains(keys(lb), "target_group_arn")
  }

  name     = join("-", [var.program, var.project, var.environment, var.name, "tg", each.key])
  port     = each.value.container_port
  protocol = each.value.protocol
  vpc_id   = var.vpc_id

  target_type = "ip"

  health_check {
    path                = each.value.health_check_path
    matcher             = "200-399"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }

  tags = var.tags
}

resource "aws_service_discovery_service" "this" {
  for_each = {
    for sd in var.service_discovery :
    "${sd.container_name}-${sd.container_port}" => sd
    if !contains(keys(sd), "registry_arn")
  }

  name         = var.name
  namespace_id = each.value.namespace_id

  dns_config {
    namespace_id = each.value.namespace_id
    dns_records {
      type = "A"
      ttl  = try(each.value.dns_ttl, 10)
    }
    routing_policy = try(each.value.routing_policy, "WEIGHTED")
  }

  health_check_custom_config {
    failure_threshold = 1
  }

  tags = var.tags
}
